<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button icon="ArrowLeft" @click="goBack">返回楼宇列表</el-button>
        <span class="building-info">{{ buildingName }} - 楼层管理</span>
      </div>
    </div>

    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="楼层名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入楼层名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="楼宇选择" prop="buildingId">
        <el-select v-model="queryParams.buildingId" placeholder="请选择楼宇" clearable style="width: 200px" @change="handleBuildingChange">
          <el-option
            v-for="building in buildingList"
            :key="building.id"
            :label="building.name"
            :value="building.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['building:floor:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['building:floor:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['building:floor:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['building:floor:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['building:floor:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 楼层表格 -->
    <el-table v-loading="loading" :data="floorList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="楼宇名称" align="center" prop="buildingNameFormatted" width="150" />
      <el-table-column label="楼层名称" align="center" prop="floorNameFormatted" width="150" />
      <el-table-column label="总占地面积" align="center" prop="coverAreaFormatted" width="130" />
      <el-table-column label="总建筑面积" align="center" prop="buildingAreaFormatted" width="130" />
      <el-table-column label="单元数量" align="center" prop="unitNum" width="120">
        <template #default="scope">
          <span>{{ scope.row.unitNum || 0 }}个</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="primary" icon="House" @click="handleUnits(scope.row)">单元</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改楼层对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="floorRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="楼层号" prop="name">
          <el-input-number
            v-model="form.name"
            :min="1"
            :max="100"
            :precision="0"
            placeholder="请输入楼层号"
            style="width: 200px"
          />
          <span style="margin-left: 8px; color: #909399;">F</span>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Floor">
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { parseTime } from '@/utils/ruoyi'
import { getToken } from '@/utils/auth'
import { listFloor, getFloor, addFloor, updateFloor, delFloor } from '@/api/building/floor'
import { getBuilding, listBuilding } from '@/api/building/building'

const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()

// 页面数据
const floorList = ref([])
const buildingList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref('')
const buildingId = ref(null)
const buildingName = ref('')
const uploadRef = ref()

// 用户导入参数
const upload = reactive({
  // 是否显示弹出层
  open: false,
  // 弹出层标题（导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/building/floor/importData"
})

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  buildingId: null,
  name: null
})

// 表单数据
const form = ref({})
const floorRef = ref()

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '楼层号不能为空', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '楼层号必须在1-100之间', trigger: 'blur' }
  ]
}

/** 格式化面积显示 */
const formatArea = (area) => {
  if (!area) return '-'
  return `${area}㎡`
}

/** 根据楼宇ID获取楼宇名称 */
const getBuildingName = (buildingId) => {
  if (!buildingId || !buildingList.value.length) return '-'
  const building = buildingList.value.find(item => item.id === buildingId)
  return building ? building.name : '-'
}

/** 格式化楼层名称显示 */
const formatFloorName = (name) => {
  if (!name) return '-'
  return `${name}F`
}

/** 获取楼宇列表 */
const getBuildingList = async () => {
  try {
    const response = await listBuilding({ pageSize: 1000 })
    buildingList.value = response.rows || []
  } catch (error) {
    console.error('获取楼宇列表失败:', error)
    ElMessage.error('获取楼宇列表失败')
  }
}

/** 获取楼宇信息 */
const getBuildingInfo = async (id) => {
  try {
    const response = await getBuilding(id)
    buildingName.value = response.data?.name || '未知楼宇'
  } catch (error) {
    console.error('获取楼宇信息失败:', error)
    buildingName.value = '未知楼宇'
  }
}

/** 查询楼层列表 */
const getList = async () => {
  loading.value = true
  try {
    const response = await listFloor(queryParams.value)
    // 预处理数据，添加格式化字段
    floorList.value = (response.rows || []).map(item => ({
      ...item,
      buildingNameFormatted: getBuildingName(item.buildingId),
      floorNameFormatted: formatFloorName(item.name),
      coverAreaFormatted: formatArea(item.coverArea),
      buildingAreaFormatted: formatArea(item.buildingArea)
    }))
    total.value = response.total || 0
  } catch (error) {
    console.error('获取楼层列表失败:', error)
    ElMessage.error('获取楼层列表失败')
    floorList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

/** 楼宇选择变化处理 */
const handleBuildingChange = (selectedBuildingId) => {
  queryParams.value.buildingId = selectedBuildingId
  if (selectedBuildingId) {
    getBuildingInfo(selectedBuildingId)
  }
  handleQuery()
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm('queryRef')
  // 重置时保持当前buildingId
  queryParams.value.buildingId = buildingId.value
  handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset()
  open.value = true
  title.value = '添加楼层'
}

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset()
  const floorId = row ? row.id : ids.value[0]

  try {
    const response = await getFloor(floorId)
    form.value = response.data
    form.value.name = Number(response.data.name)
    open.value = true
    title.value = '修改楼层'
  } catch (error) {
    console.error('获取楼层详情失败:', error)
    ElMessage.error('获取楼层详情失败')
  }
}

/** 提交按钮 */
const submitForm = async () => {
  if (!floorRef.value) return
  try {
    console.log('form.value', form.value)
    await floorRef.value.validate()

    const formData = {
      ...form.value,
      buildingId: buildingId.value
    }

    if (form.value.id != null) {
      // 修改
      await updateFloor(formData)
      ElMessage.success('修改成功')
    } else {
      // 新增
      await addFloor(formData)
      ElMessage.success('新增成功')
    }

    open.value = false
    getList()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败，请重试')
  }
}

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const floorIds = row ? [row.id] : ids.value

  if (floorIds.length === 0) {
    ElMessage.warning('请选择要删除的楼层')
    return
  }

  // 获取要删除的楼层名称（用于确认提示）
  const floorNames = row
    ? [row.floorNameFormatted || row.name || `楼层${row.id}`]
    : floorList.value
        .filter(item => floorIds.includes(item.id))
        .map(item => item.floorNameFormatted || item.name || `楼层${item.id}`)

  try {
    const message = floorIds.length === 1
      ? `是否确认删除楼层"${floorNames[0]}"？`
      : `是否确认删除选中的 ${floorIds.length} 个楼层？\n楼层：${floorNames.join('、')}`

    await ElMessageBox.confirm(message, '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: false
    })

    await delFloor(floorIds.join(','))
    ElMessage.success(`成功删除 ${floorIds.length} 个楼层`)
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败，请重试')
    }
  }
}

/** 单元管理按钮操作 */
const handleUnits = (row) => {
  router.push('/building/units/' + buildingId.value + '/' + row.id)
}

/** 返回楼宇列表 */
const goBack = () => {
  router.push('/building/building')
}

/** 取消按钮 */
const cancel = () => {
  open.value = false
  reset()
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy.download('building/floor/export', {
    ...queryParams.value
  }, `楼层信息_${new Date().getTime()}.xlsx`)
}

/** 导入按钮操作 */
const handleImport = () => {
  upload.title = "楼层信息导入"
  upload.open = true
}

/** 下载模板操作 */
const importTemplate = () => {
  proxy.download("building/floor/downTemplate", {
  }, `楼层信息_${new Date().getTime()}.xlsx`)
}

/** 文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true
}

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false
  upload.isUploading = false
  uploadRef.value.handleRemove(file)
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true })
  getList()
}

/** 提交上传文件 */
const submitFileForm = () => {
  uploadRef.value.submit()
}

/** 表单重置 */
const reset = () => {
  form.value = {
    id: null,
    name: null,
    remark: null
  }
  if (floorRef.value) {
    floorRef.value.resetFields()
  }
}

// 初始化
onMounted(async () => {
  // 1. 从URL路由参数中获取buildingId
  buildingId.value = Number(route.params.buildingId)
  queryParams.value.buildingId = buildingId.value

  // 2. 先获取所有楼宇列表，确保楼宇名称能正确显示
  await getBuildingList()

  // 3. 获取当前楼宇信息用于页面标题显示
  if (buildingId.value) {
    await getBuildingInfo(buildingId.value)
  }

  // 4. 获取指定楼宇下的楼层列表数据
  getList()
})
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
  padding: 16px 0;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.building-info {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}
</style>
