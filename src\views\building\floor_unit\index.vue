<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button icon="ArrowLeft" @click="goBack">返回楼层列表</el-button>
        <span class="building-info">{{ buildingName }} - {{ floorNumber }}F - 单元管理</span>
      </div>
    </div>

    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="楼宇选择" prop="buildingId">
        <el-select v-model="queryParams.buildingId" placeholder="请选择楼宇" clearable style="width: 200px" @change="handleBuildingChange">
          <el-option
            v-for="building in buildingList"
            :key="building.id"
            :label="building.name"
            :value="building.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="楼层选择" prop="floorId">
        <el-select v-model="queryParams.floorId" placeholder="请选择楼层" clearable style="width: 200px" @change="handleFloorChange">
          <el-option
            v-for="floor in floorList"
            :key="floor.id"
            :label="floor.name + 'F'"
            :value="floor.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['building:floor_unit:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['building:floor_unit:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="CopyDocument"
          :disabled="single"
          @click="handleCopy"
          v-hasPermi="['building:floor_unit:copy']"
          >复制</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['building:floor_unit:remove']"
          >删除</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport" v-hasPermi="['building:floor_unit:import']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['building:floor_unit:export']"
          >导出</el-button
        >
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="floor_unitList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="80" align="center" />
      <el-table-column label="ID" align="center" prop="id"  />
      <el-table-column label="单元编码" align="center" prop="code"  />
      <el-table-column label="楼宇名称" align="center" prop="buildingNameFormatted"  />
      <el-table-column label="楼层名称" align="center" prop="floorNameFormatted" />
      <el-table-column label="装修类型" align="center" prop="zxType" >
        <template #default="scope"> <dict-tag :options="floor_unit_zx_type" :value="scope.row.zxType" /> </template>
      </el-table-column>
      <el-table-column label="已通水电" align="center" prop="isOpenSd">
        <template #default="scope"> <dict-tag :options="commonstatus" :value="scope.row.isOpenSd" /> </template>
      </el-table-column>
      <el-table-column label="功能区" align="center" prop="isFunArea">
        <template #default="scope"> <dict-tag :options="commonstatus" :value="scope.row.isFunArea" /> </template>
      </el-table-column>
      <el-table-column label="实用面积" align="center" prop="coverAreaFormatted"  />
      <el-table-column label="建筑面积" align="center" prop="buildingAreaFormatted"  />
      <el-table-column label="朝向" align="center" prop="chaoxiang"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="130" fixed="right">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['building:floor_unit:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['building:floor_unit:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改单元管理对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="floor_unitRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="单元编码" prop="code" label-width="170">
              <el-input
                v-model="form.code"
                placeholder="请输入单元编码"
                style="width: 280px"
                :formatter="codeFormatter"
                :parser="codeParser"
                maxlength="4"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="装修类型" prop="zxType" label-width="170">
              <el-select v-model="form.zxType" placeholder="请选择装修类型" style="width: 280px">
                <el-option
                  v-for="dict in floor_unit_zx_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="已通水电" prop="isOpenSd" label-width="170">
              <el-select v-model="form.isOpenSd" placeholder="请选择已通水电" style="width: 280px">
                <el-option
                  v-for="dict in commonstatus"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="功能区" prop="isFunArea" label-width="170">
              <el-select v-model="form.isFunArea" placeholder="请选择功能区" style="width: 280px">
                <el-option
                  v-for="dict in commonstatus"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实用面积" prop="coverArea" label-width="170">
              <el-input v-model="form.coverArea" placeholder="请输入实用面积" style="width: 280px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="建筑面积" prop="buildingArea" label-width="170">
              <el-input v-model="form.buildingArea" placeholder="请输入建筑面积" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="朝向" prop="chaoxiang" label-width="170">
              <el-input v-model="form.chaoxiang" placeholder="请输入朝向" style="width: 280px" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport + (queryParams.buildingId ? '&buildingId=' + queryParams.buildingId : '') + (queryParams.floorId ? '&floorId=' + queryParams.floorId : '')"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip"><el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据</div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 复制单元对话框 -->
    <el-dialog title="复制单元" v-model="copyDialog.open" width="400px" append-to-body>
      <el-form ref="copyFormRef" :model="copyDialog.form" :rules="copyDialog.rules" label-width="100px">
        <el-form-item label="单元信息">
          <span>{{ copyDialog.unitInfo }}</span>
        </el-form-item>
        <el-form-item label="复制数量" prop="num">
          <el-input-number
            v-model="copyDialog.form.num"
            :min="1"
            :max="50"
            placeholder="请输入复制数量"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="copyDialog.open = false">取 消</el-button>
          <el-button type="primary" @click="submitCopyForm" :loading="copyDialog.loading">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="floor_unit">
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getToken } from "@/utils/auth";
import { listFloorUnit, getFloorUnit, delFloorUnit, addFloorUnit, updateFloorUnit, copyFloorUnit } from "@/api/building/floor_unit";
import { listBuilding } from "@/api/building/building";
import { listFloor } from "@/api/building/floor";
const { proxy } = getCurrentInstance();
const route = useRoute()
const router = useRouter()
const { floor_unit_zx_type, commonstatus } = proxy.useDict("floor_unit_zx_type", "commonstatus");
const floor_unitList = ref([]);
const buildingList = ref([]);
const floorList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const buildingId = ref(null);
const buildingName = ref('');
const floorId = ref(null);
const floorNumber = ref('');
const daterangeCreateTime = ref([]);
const daterangeUpdateTime = ref([]);
const copyFormRef = ref();

// 复制对话框数据
const copyDialog = reactive({
  open: false,
  loading: false,
  floorUnitId: null,
  unitInfo: '',
  form: {
    num: 1
  },
  rules: {
    num: [
      { required: true, message: '复制数量不能为空', trigger: 'blur' },
      { type: 'number', min: 1, max: 50, message: '复制数量必须在1-50之间', trigger: 'blur' }
    ]
  }
});
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    code: null,
    buildingId: null,
    floorId: null,
    zxType: null,
    isOpenSd: null,
    isFunArea: null,
  },
  rules: ref({}),
});
const { queryParams, form, rules } = toRefs(data);

/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层
  open: false,
  // 弹出层标题（导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/building/floor_unit/importData",
});

/** 格式化面积显示 */
const formatArea = (area) => {
  if (!area) return '-'
  return `${area}㎡`
}

/** 根据楼宇ID获取楼宇名称 */
const getBuildingName = (buildingId) => {
  if (!buildingId || !buildingList.value.length) return '-'
  const building = buildingList.value.find(item => item.id == buildingId)
  return building ? building.name : '-'
}

/** 根据楼层ID获取楼层名称 */
const getFloorName = (floorId) => {
  if (!floorId || !floorList.value.length) return '-'
  const floor = floorList.value.find(item => item.id == floorId)
  return floor ? `${floor.name}F` : '-'
}

/** 获取楼宇列表 */
const getBuildingList = async () => {
  try {
    const response = await listBuilding({ pageSize: 1000 })
    buildingList.value = response.rows || []
  } catch (error) {
    console.error('获取楼宇列表失败:', error)
    proxy.$modal.msgError('获取楼宇列表失败')
  }
}

/** 获取楼层列表 */
const getFloorList = async (buildingId) => {
  if (!buildingId) {
    floorList.value = []
    return
  }

  try {
    const response = await listFloor({ buildingId: buildingId, pageSize: 1000 })
    floorList.value = response.rows || []
  } catch (error) {
    console.error('获取楼层列表失败:', error)
    proxy.$modal.msgError('获取楼层列表失败')
  }
}

/** 从楼宇列表中获取楼宇信息 */
const getBuildingInfo = (id) => {
  if (!id || !buildingList.value.length) {
    buildingName.value = '未知楼宇'
    return
  }
  const building = buildingList.value.find(item => item.id == id)
  buildingName.value = building ? building.name : '未知楼宇'
}

/** 从楼层列表中获取楼层信息 */
const getFloorInfo = (id) => {
  if (!id || !floorList.value.length) {
    floorNumber.value = '未知楼层'
    return
  }
  const floor = floorList.value.find(item => item.id == id)
  floorNumber.value = floor ? floor.name : '未知楼层'
}

/** 楼宇选择变化处理 */
const handleBuildingChange = async (selectedBuildingId) => {
  queryParams.value.buildingId = selectedBuildingId
  queryParams.value.floorId = null // 清空楼层选择

  // 重新获取楼层列表
  await getFloorList(selectedBuildingId)

  // 如果选择了楼宇，获取楼宇信息
  if (selectedBuildingId) {
    getBuildingInfo(selectedBuildingId)
  }

  handleQuery()
}

/** 楼层选择变化处理 */
const handleFloorChange = (selectedFloorId) => {
  queryParams.value.floorId = selectedFloorId

  // 如果选择了楼层，获取楼层信息
  if (selectedFloorId) {
    getFloorInfo(selectedFloorId)
  }

  handleQuery()
}

/** 单元编码格式化器 - 显示时自动补零 */
const codeFormatter = (value) => {
  if (!value) return ''

  // 只保留数字
  const numericValue = value.toString().replace(/[^0-9]/g, '')

  if (!numericValue) return ''

  const numValue = parseInt(numericValue)

  // 小于10的前面补0，大于等于10的去掉前面的0
  if (numValue > 0 && numValue < 10) {
    return numValue.toString().padStart(2, '0')
  } else if (numValue >= 10) {
    // 去掉前面的0，返回纯数字字符串
    return numValue.toString()
  }

  return numericValue
}

/** 单元编码解析器 - 输入时只允许数字 */
const codeParser = (value) => {
  if (!value) return ''

  // 只保留数字，其他字符清空
  const numericValue = value.replace(/[^0-9]/g, '')

  // 限制最大长度为4位
  return numericValue.slice(0, 4)
}

/** 复制按钮操作 */
const handleCopy = () => {
  const floorUnitId = ids.value[0]
  const selectedUnit = floor_unitList.value.find(item => item.id === floorUnitId)

  if (!selectedUnit) {
    proxy.$modal.msgError('请选择要复制的单元')
    return
  }

  // 确保复制的单元属于当前筛选选中的楼宇和楼层
  if (queryParams.value.buildingId && selectedUnit.buildingId !== queryParams.value.buildingId) {
    proxy.$modal.msgError('只能复制当前筛选楼宇下的单元')
    return
  }

  if (queryParams.value.floorId && selectedUnit.floorId !== queryParams.value.floorId) {
    proxy.$modal.msgError('只能复制当前筛选楼层下的单元')
    return
  }

  copyDialog.floorUnitId = selectedUnit.id
  copyDialog.unitInfo = `${selectedUnit.buildingNameFormatted} - ${selectedUnit.floorNameFormatted} - ${selectedUnit.code}`
  copyDialog.form.num = 1
  copyDialog.open = true
}

/** 提交复制表单 */
const submitCopyForm = async () => {
  if (!copyFormRef.value) return

  try {
    await copyFormRef.value.validate()
    copyDialog.loading = true

    await copyFloorUnit(copyDialog.floorUnitId, copyDialog.form.num)

    proxy.$modal.msgSuccess(`成功复制 ${copyDialog.form.num} 个单元`)
    copyDialog.open = false
    getList()
  } catch (error) {
    console.error('复制单元失败:', error)
    proxy.$modal.msgError('复制单元失败，请重试')
  } finally {
    copyDialog.loading = false
  }
}

/** 返回楼层列表 */
const goBack = () => {
  router.push('/building/floor/' + buildingId.value)
}

/** 查询单元管理列表 */
function getList() {
  loading.value = true;
  listFloorUnit(queryParams.value).then((response) => {
    // 预处理数据，添加格式化字段
    floor_unitList.value = (response.rows || []).map(item => ({
      ...item,
      buildingNameFormatted: getBuildingName(item.buildingId),
      floorNameFormatted: getFloorName(item.floorId),
      coverAreaFormatted: formatArea(item.coverArea),
      buildingAreaFormatted: formatArea(item.buildingArea)
    }))
    total.value = response.total;
    loading.value = false;
  });
}
// 取消按钮
function cancel() {
  open.value = false;
  reset();
}
// 表单重置
function reset() {
  form.value = {
    id: null,
    code: null,
    zxType: null,
    isOpenSd: null,
    isFunArea: null,
    coverArea: null,
    buildingArea: null,
    chaoxiang: null,
    remark: null,
  };
  proxy.resetForm("floor_unitRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  // 重置时保持当前buildingId和floorId
  queryParams.value.buildingId = buildingId.value;
  queryParams.value.floorId = floorId.value;
  handleQuery();
}
// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
  reset();
  // 自动填充当前筛选选中的楼宇ID和楼层ID
  if (queryParams.value.buildingId) {
    form.value.buildingId = queryParams.value.buildingId;
  }
  if (queryParams.value.floorId) {
    form.value.floorId = queryParams.value.floorId;
  }
  open.value = true;
  title.value = "添加单元管理";
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getFloorUnit(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改单元管理";
  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["floor_unitRef"].validate((valid) => {
    if (valid) {
      // 自动填充当前筛选选中的楼宇和楼层ID
      const formData = {
        ...form.value,
        buildingId: queryParams.value.buildingId || form.value.buildingId,
        floorId: queryParams.value.floorId || form.value.floorId,
        // 单元编码已经在失焦时格式化过了，直接使用
        code: form.value.code
      }

      if (form.value.id != null) {
        updateFloorUnit(formData).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addFloorUnit(formData).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm("是否确认删除选中的数据，此次将删除" + _ids.length + "条数据？")
    .then(function () {
      return delFloorUnit(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "building/floor_unit/export",
    {
      ...queryParams.value,
    },
    `单元管理_${new Date().getTime()}.xlsx`
  );
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "单元管理导入";
  upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
  const params = {};
  // 包含当前筛选选中的楼宇ID和楼层ID
  if (queryParams.value.buildingId) {
    params.buildingId = queryParams.value.buildingId;
  }
  if (queryParams.value.floorId) {
    params.floorId = queryParams.value.floorId;
  }
  proxy.download("building/floor_unit/downTemplate", params, `单元管理_${new Date().getTime()}.xlsx`);
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}
// 页面初始化
onMounted(async () => {
  // 1. 从URL路由参数中获取buildingId和floorId
  buildingId.value = Number(route.params.buildingId)
  floorId.value = Number(route.params.floorId)
  queryParams.value.buildingId = buildingId.value
  queryParams.value.floorId = floorId.value

  // 2. 先获取所有楼宇列表，确保楼宇选择器有数据
  await getBuildingList()

  // 3. 获取当前楼宇的楼层列表
  if (buildingId.value) {
    await getFloorList(buildingId.value)
  }

  // 4. 从楼宇列表中获取楼宇信息用于页面标题显示
  if (buildingId.value) {
    getBuildingInfo(buildingId.value)
  }

  // 5. 从楼层列表中获取楼层信息用于页面标题显示
  if (floorId.value) {
    getFloorInfo(floorId.value)
  }

  // 6. 获取指定楼宇和楼层下的单元列表数据
  getList()
})
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
  padding: 16px 0;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.building-info {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}
</style>
