import request from '@/utils/request'
// 查询企业信息临时表列表
export function listEnterpriseTmp(query) {
  return request({
    url: '/enterprise/enterprise_tmp/list',
    method: 'get',
    params: query
  })
}

// 查询企业信息临时表详细
export function getEnterpriseTmp(id) {
  return request({
    url: '/enterprise/enterprise_tmp/' + id,
    method: 'get'
  })
}

// 新增企业信息临时表
export function addEnterpriseTmp(data) {
  return request({
    url: '/enterprise/enterprise_tmp/add',
    method: 'post',
    data: data
  })
}

// 修改企业信息临时表
export function updateEnterpriseTmp(data) {
  return request({
    url: '/enterprise/enterprise_tmp/edit',
    method: 'post',
    data: data
  })
}

// 删除企业信息临时表
export function delEnterpriseTmp(id) {
  return request({
    url: '/enterprise/enterprise_tmp/' + id,
    method: 'delete'
  })
}

