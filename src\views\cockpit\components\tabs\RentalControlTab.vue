<template>
  <!-- 租控统计 Tab -->
  <div class="tab-pane">
    <div class="rental-statistics-section">
      <!-- 统计信息区域 -->
      <div class="statistics-info-section">
        <div class="info-cards">
          <!-- 总单元统计卡片 -->
          <div class="info-card">
            <div class="card-header">
              <el-icon class="card-icon"><OfficeBuilding /></el-icon>
              <span class="card-title">总单元统计</span>
            </div>
            <div class="card-content">
              <div class="info-item">
                <span class="label">总单元数</span>
                <span class="value highlight">{{ totalUnitsCount }}个</span>
              </div>
              <div class="info-item">
                <span class="label">楼层数</span>
                <span class="value">{{ floorData.length }}层</span>
              </div>
            </div>
          </div>

          <!-- 出租统计卡片 -->
          <div class="info-card">
            <div class="card-header">
              <el-icon class="card-icon"><DataAnalysis /></el-icon>
              <span class="card-title">出租统计</span>
            </div>
            <div class="card-content">
              <div class="info-item">
                <span class="label">已出租单元</span>
                <span class="value occupancy">{{ getLegendCount("已出租") }}个</span>
              </div>
              <div class="info-item">
                <span class="label">空置单元</span>
                <span class="value">{{ getLegendCount("空置") }}个</span>
              </div>
            </div>
          </div>

          <!-- 入住率统计卡片 -->
          <div class="info-card">
            <div class="card-header">
              <el-icon class="card-icon"><TrendCharts /></el-icon>
              <span class="card-title">入住率统计</span>
            </div>
            <div class="card-content">
              <div class="info-item">
                <span class="label">当前入住率</span>
                <span class="value occupancy-rate">{{ getOccupancyRate() }}%</span>
              </div>
              <div class="info-item">
                <span class="label">空置率</span>
                <span class="value">{{ 100 - getOccupancyRate() }}%</span>
              </div>
            </div>
          </div>

          <!-- 其他状态统计卡片 -->
          <div class="info-card">
            <div class="card-header">
              <el-icon class="card-icon"><House /></el-icon>
              <span class="card-title">其他状态</span>
            </div>
            <div class="card-content">
              <div class="info-item">
                <span class="label">功能区</span>
                <span class="value">{{ getLegendCount("功能区") }}个</span>
              </div>
              <div class="info-item">
                <span class="label">在淡客户</span>
                <span class="value">{{ getLegendCount("在淡客户") }}个</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, computed } from "vue";
import { OfficeBuilding, DataAnalysis, TrendCharts, House } from "@element-plus/icons-vue";

const props = defineProps({
  buildingData: {
    type: Object,
    default: () => ({}),
  },
});

// 定义租控状态类型和颜色
const rentalStatuses = [
  { name: "已出租", color: "#FFD700" }, // 黄色
  { name: "功能区", color: "#FF6B6B" }, // 红色
  { name: "在淡客户", color: "#4ECDC4" }, // 青色
  { name: "不存在", color: "#95A5A6" }, // 灰色
  { name: "空置", color: "#00FEFF" }, // 蓝色
  { name: "其他", color: "#9B59B6" }, // 紫色
];

// 获取状态颜色
const getStatusColor = (statusName) => {
  const status = rentalStatuses.find((s) => s.name === statusName);
  return status ? status.color : "#666";
};

// 生成楼层数据
const floorData = computed(() => {
  // 模拟楼层数据，每层包含不同数量和状态的单元
  const floors = [];
  const statuses = ["已出租", "功能区", "在淡客户", "不存在", "空置", "其他"];

  // 生成30层楼的数据，从高层到低层排序（30F -> 1F）
  for (let floor = 30; floor >= 1; floor--) {
    const unitsPerFloor = Math.floor(Math.random() * 8) + 4; // 每层4-12个单元
    const units = [];

    for (let unit = 1; unit <= unitsPerFloor; unit++) {
      const unitNumber = `${floor}${String(unit).padStart(2, "0")}`;
      const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];

      // 根据状态生成不同的信息
      let info = "";
      if (randomStatus === "已出租") {
        const companies = [
          "腾讯科技",
          "阿里巴巴",
          "百度网络",
          "字节跳动",
          "美团",
          "滴滴出行",
          "小米科技",
          "华为技术",
          "京东集团",
          "网易科技",
        ];
        info = companies[Math.floor(Math.random() * companies.length)];
      } else if (randomStatus === "功能区") {
        const areas = ["会议室", "休息区", "茶水间", "打印区", "接待区", "健身房", "餐厅"];
        info = areas[Math.floor(Math.random() * areas.length)];
      } else if (randomStatus === "在淡客户") {
        const prospects = ["意向客户A", "意向客户B", "洽谈中", "考察中", "预约看房"];
        info = prospects[Math.floor(Math.random() * prospects.length)];
      }

      units.push({
        unitNumber,
        info,
        status: randomStatus,
        color: getStatusColor(randomStatus),
        tooltip: `${floor}F-${unitNumber} - ${info || randomStatus}`,
      });
    }

    floors.push({
      number: floor,
      units,
    });
  }

  return floors;
});

// 计算总单元数
const totalUnitsCount = computed(() => {
  return floorData.value.reduce((total, floor) => total + floor.units.length, 0);
});



// 获取图例统计数据
const getLegendCount = (statusName) => {
  let count = 0;
  floorData.value.forEach((floor) => {
    floor.units.forEach((unit) => {
      if (unit.status === statusName) {
        count++;
      }
    });
  });
  return count;
};

// 获取入住率
const getOccupancyRate = () => {
  const total = totalUnitsCount.value;
  if (total === 0) return 0;
  const occupied = getLegendCount("已出租");
  return Math.round((occupied / total) * 100);
};

// 暴露方法给父组件
defineExpose({
  resetFilter: () => {
    // 统计页面无需重置过滤器
    console.log("租控统计页面：无需重置过滤器");
  },
});
</script>

<style scoped>
/* 租控统计样式 */
.rental-statistics-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
}

/* 统计信息区域 */
.statistics-info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.info-card {
  background: linear-gradient(135deg, rgba(20, 50, 120, 0.4) 0%, rgba(25, 55, 125, 0.6) 100%);
  border: 1px solid rgba(0, 149, 255, 0.2);
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  height: fit-content;
}

.info-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 254, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.info-card:hover::before {
  left: 100%;
}

.info-card:hover {
  border-color: rgba(0, 149, 255, 0.4);
  box-shadow: 0 0 20px rgba(0, 149, 255, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 149, 255, 0.2);
}

.card-icon {
  font-size: 18px;
  color: #00feff;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #00feff;
}

.card-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.value {
  color: #00feff;
  font-weight: 600;
  font-size: 18px;
  text-shadow: 0 0 5px rgba(0, 254, 255, 0.3);
}

.value.highlight {
  color: #ffa726;
  text-shadow: 0 0 5px rgba(255, 167, 38, 0.3);
}

.value.occupancy {
  color: #4caf50;
  text-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
}

.value.occupancy-rate {
  color: #ff9800;
  font-size: 24px;
  font-weight: 700;
  text-shadow: 0 0 8px rgba(255, 152, 0, 0.4);
}


</style>
