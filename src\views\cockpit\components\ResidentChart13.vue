<template>
  <div
    class="w-[618px] h-[314px] p-[20px] mt-1 relative bg-no-repeat bg-contain bg-[url(@/assets/cockpit/bg/border.png)]">
    <div class="w-[200px] h-[30px] leading-[30px] text-white text-lg mb-3 pl-2 flex items-center">
      <span class="w-[4px] h-[18px] bg-[#00feff] mr-2 inline-block"></span>
      小区楼栋入住率
    </div>
    <div ref="chartRef" class="w-full h-[231px]"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts";

// 图表引用
const chartRef = ref(null);
let chartInstance = null;

onMounted(() => {
  initChart();
  // 添加窗口大小变化的监听，自动调整图表大小
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  // 清除图表实例
  chartInstance && chartInstance.dispose();
  // 移除窗口大小变化的监听
  window.removeEventListener('resize', handleResize);
});

// 处理窗口大小变化，调整图表大小
const handleResize = () => {
  chartInstance && chartInstance.resize();
};

// 初始化图表
const initChart = () => {
  // 检查DOM元素是否存在
  if (!chartRef.value) return;

  // 如果图表实例已存在，则销毁
  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartRef.value, "macarons");

  // 从图片中提取的实际小区数据（按入住率从高到低排序）
  const communities = [
    // 西区
    { name: '依云一期(18-29)', rate: 87.25, area: '西区' },
    { name: '依云普盛花园', rate: 81.71, area: '南区' },
    { name: '恒樾普盛御府', rate: 73.64, area: '南区' },
    { name: '保利东坝花园', rate: 74.93, area: '东区' },
    { name: '依云二期(30-39)', rate: 63.55, area: '西区' },
    { name: '依云三期(40-49)', rate: 64.05, area: '西区' },
    { name: '碧桂园新城之光花园一期', rate: 63.85, area: '西区' },
    { name: '依云一期别墅', rate: 65.33, area: '西区' },
    { name: '碧桂园新城之光花园二期', rate: 61.08, area: '西区' },
    { name: '碧桂园小区', rate: 57.98, area: '南区' },
    { name: '泰禾红郡府二期', rate: 54.60, area: '南区' },
    { name: '泰禾红郡府一期', rate: 54.05, area: '南区' },
    { name: '华润橡树公馆一期', rate: 52.08, area: '西区' },
    { name: '保利东旭花园', rate: 51.11, area: '东区' },
    { name: '泰禾红郡府三期', rate: 50.26, area: '南区' },
    { name: '华润橡树公馆二期', rate: 49.80, area: '西区' },
    { name: '保利东滨花园', rate: 49.39, area: '东区' },
    { name: '羽云府', rate: 47.99, area: '南区' },
    { name: '保利东御花园', rate: 55.48, area: '东区' },
    { name: '保利东嘉花园', rate: 57.34, area: '东区' },
    { name: '保利东旭花园', rate: 42.76, area: '东区' },
    { name: '保利天环花园', rate: 39.15, area: '中区' },
    { name: '东茂广场', rate: 39.45, area: '中区' },
    { name: '恒樾普盛泰园', rate: 38.75, area: '南区' },
    { name: '保利天瑞广场', rate: 34.34, area: '中区' },
    { name: '保利西院湾花园', rate: 32.60, area: '中区' },
    { name: '春风南岸', rate: 10.99, area: '南区' },
    { name: '华发逸庭', rate: 1.77, area: '西区' }
  ];

  // 年份作为X轴
  const years = ['2018年', '2019年', '2020年', '2021年', '2022年', '2023年'];

  // 对数据进行处理，只选择前15个小区，以避免图例过多
  const topCommunities = communities.slice(0, 15);

  // 为每个小区生成模拟的历史数据（基于当前入住率，逐年递减）
  const generateHistoricalData = (currentRate) => {
    // 模拟历史数据，假设每年的入住率递减，最早年份约为当前的30%-50%
    const rates = [];
    for (let i = 5; i >= 0; i--) {
      const factor = 0.5 + (i * 0.1); // 从0.5到1.0的系数
      rates.push(Math.round((currentRate * factor) * 100) / 100);
    }
    return rates;
  };

  // 生成每个小区的历史数据
  const communitiesData = topCommunities.map(community => {
    return {
      name: community.name,
      area: community.area,
      data: generateHistoricalData(community.rate)
    };
  });

  // 紫色系渐变色系列
  const colors = [
    // 西区 - 蓝紫色系
    new echarts.graphic.LinearGradient(0, 0, 1, 0, [
      { offset: 0, color: '#8e79f5' },
      { offset: 1, color: '#6c5dd3' }
    ]),
    // 南区 - 紫红色系
    new echarts.graphic.LinearGradient(0, 0, 1, 0, [
      { offset: 0, color: '#ba7fec' },
      { offset: 1, color: '#9d4de0' }
    ]),
    // 东区 - 粉紫色系
    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
      { offset: 0, color: '#c679e3' },
      { offset: 1, color: '#a742ea' }
    ]),
    // 中区 - 靛蓝色系
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#7d71f2' },
      { offset: 1, color: '#554bc5' }
    ]),
    // 其他颜色
    new echarts.graphic.LinearGradient(0, 0, 1, 0, [
      { offset: 0, color: '#9597e4' },
      { offset: 1, color: '#7986cb' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
      { offset: 0, color: '#d8a1ff' },
      { offset: 1, color: '#b57dee' }
    ]),
    // 添加更多颜色
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#b388ff' },
      { offset: 1, color: '#8c44ff' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 1, 0, [
      { offset: 0, color: '#ff9ce7' },
      { offset: 1, color: '#fa65d1' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
      { offset: 0, color: '#9fa8da' },
      { offset: 1, color: '#7986cb' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#738bff' },
      { offset: 1, color: '#5c6bc0' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 1, 0, [
      { offset: 0, color: '#aa8de8' },
      { offset: 1, color: '#7e57c2' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
      { offset: 0, color: '#ff8aef' },
      { offset: 1, color: '#ea80fc' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#a290f8' },
      { offset: 1, color: '#6f74dd' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 1, 0, [
      { offset: 0, color: '#c39cf7' },
      { offset: 1, color: '#9575cd' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
      { offset: 0, color: '#bb9af8' },
      { offset: 1, color: '#9c6efc' }
    ])
  ];

  // 构建系列数据，每个小区一个系列
  const series = communitiesData.map((community, index) => {
    return {
      name: community.name,
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 5,
      data: community.data,
      lineStyle: {
        width: 2
      }
    };
  });

  // 创建折线图配置
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: function (params) {
        let result = `${params[0].axisValue}<br/>`;

        // 按入住率从高到低排序
        params.sort((a, b) => b.value - a.value);

        params.forEach(param => {
          const color = param.color;
          const marker = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
          const communityData = communitiesData.find(c => c.name === param.seriesName);
          const area = communityData ? ` (${communityData.area})` : '';
          result += `${marker}${param.seriesName}${area}: ${param.value}%<br/>`;
        });

        return result;
      },
      backgroundColor: 'rgba(10, 30, 50, 0.7)',
      borderColor: 'rgba(48, 207, 208, 0.3)',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 8,
      top: 10,
      bottom: 20,
      data: topCommunities.map(c => c.name),
      textStyle: {
        color: '#fff',
        fontSize: 12,
        lineHeight: 13
      },
      padding: [10, 0, 0, 0],
      pageButtonItemGap: 5,
      pageButtonGap: 5,
      pageButtonPosition: 'end',
      pageFormatter: '{current}/{total}',
      pageIconColor: '#aaa',
      pageIconInactiveColor: '#2f4554',
      pageIconSize: 12,
      pageTextStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: '3%',
      right: '30%',
      bottom: '3%',
      top: '18%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: years,
        axisPointer: {
          type: 'shadow'
        },
        axisTick: {
          alignWithLabel: true,
          show: false
        },
        axisLine: {
          lineStyle: {
            color: '#1a5cd7'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 12
        }
      }
    ],
    yAxis: {
      type: 'value',
      name: '入住率(%)',
      min: 0,
      max: 100,
      interval: 20,
      nameTextStyle: {
        color: '#fff',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: '#1a5cd7'
        }
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12,
        formatter: '{value}%'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(26, 92, 215, 0.3)',
          type: 'dashed'
        }
      }
    },
    // 紫色系渐变色系列
    color: colors,
    series: series
  };

  chartInstance.setOption(option);
};
</script>

<style scoped>
/* 可以添加组件特有的样式 */
</style> 