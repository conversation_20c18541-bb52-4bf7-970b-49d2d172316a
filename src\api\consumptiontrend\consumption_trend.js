import request from '@/utils/request'
// 查询趋势图列表
export function listConsumptionTrend(query) {
  return request({
    url: '/consumptiontrend/consumption_trend/list',
    method: 'get',
    params: query
  })
}

// 查询趋势图详细
export function getConsumptionTrend(id) {
  return request({
    url: '/consumptiontrend/consumption_trend/' + id,
    method: 'get'
  })
}

// 新增趋势图
export function addConsumptionTrend(data) {
  return request({
    url: '/consumptiontrend/consumption_trend/add',
    method: 'post',
    data: data
  })
}

// 修改趋势图
export function updateConsumptionTrend(data) {
  return request({
    url: '/consumptiontrend/consumption_trend/edit',
    method: 'post',
    data: data
  })
}

// 删除趋势图
export function delConsumptionTrend(id) {
  return request({
    url: '/consumptiontrend/consumption_trend/' + id,
    method: 'delete'
  })
}

