<template>
  <!-- 统计信息 Tab -->
  <div class="tab-pane-content">
    <div class="residential-info">
      <!-- 统计信息区域 -->
      <div class="residential-info-section">
        <div class="info-cards">
          <!-- 户数统计卡片 -->
          <div class="info-card">
            <div class="card-header">
              <el-icon class="card-icon"><House /></el-icon>
              <span class="card-title">户数统计</span>
            </div>
            <div class="card-content">
              <div class="info-item">
                <span class="label">住宅总户数（建设总套数）</span>
                <span class="value highlight">{{ formatNumber(residentialData.totalHouseholds) }}户</span>
              </div>
              <div class="info-item">
                <span class="label">常住总户数</span>
                <span class="value">{{ formatNumber(residentialData.residentHouseholds) }}户</span>
              </div>
              <div class="info-item">
                <span class="label">不常住总户数</span>
                <span class="value">{{ formatNumber(residentialData.nonResidentHouseholds) }}户</span>
              </div>
              <div class="info-item">
                <span class="label">已装修不常住户数</span>
                <span class="value">{{ formatNumber(residentialData.decoratedNonResident) }}户</span>
              </div>
              <div class="info-item">
                <span class="label">未装修户数</span>
                <span class="value">{{ formatNumber(residentialData.undecorated) }}户</span>
              </div>
            </div>
          </div>

          <!-- 人口统计卡片 -->
          <div class="info-card">
            <div class="card-header">
              <el-icon class="card-icon"><User /></el-icon>
              <span class="card-title">人口统计</span>
            </div>
            <div class="card-content">
              <div class="info-item">
                <span class="label">常住总人数</span>
                <span class="value highlight">{{ formatNumber(residentialData.residentPopulation) }}人</span>
              </div>
              <div class="info-item">
                <span class="label">常住总人数变量</span>
                <span class="value" :class="getChangeClass(residentialData.populationChange)">
                  {{ formatChange(residentialData.populationChange) }}
                </span>
              </div>
              <div class="info-item">
                <span class="label">常住总户数变量</span>
                <span class="value" :class="getChangeClass(residentialData.householdChange)">
                  {{ formatChange(residentialData.householdChange) }}
                </span>
              </div>
              <div class="info-item">
                <span class="label">入住率</span>
                <span class="value occupancy">{{ formatPercentage(residentialData.occupancyRate) }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';
import { House, User } from "@element-plus/icons-vue";

const props = defineProps({
  residentialData: {
    type: Object,
    default: () => ({})
  }
});

// 格式化数字
const formatNumber = (num) => {
  if (!num || num === '暂无数据') return '暂无数据';
  return Number(num).toLocaleString();
};

// 格式化变化量
const formatChange = (change) => {
  if (!change && change !== 0) return '暂无数据';
  const prefix = change > 0 ? '+' : '';
  return `${prefix}${change}`;
};

// 格式化百分比
const formatPercentage = (rate) => {
  if (!rate && rate !== 0) return '暂无数据';
  return Number(rate).toFixed(1);
};

// 获取变化量样式类
const getChangeClass = (change) => {
  if (!change && change !== 0) return '';
  return change > 0 ? 'positive-change' : change < 0 ? 'negative-change' : 'no-change';
};
</script>

<style scoped>
/* Tab内容区域 */
.tab-pane-content {
  padding: 20px;
  height: 100%;
}

/* 小区信息布局 */
.residential-info {
  display: flex;
  gap: 25px;
  height: 100%;
}

/* 小区信息区域 */
.residential-info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-cards {
  display: flex;
  flex-direction: column;
  gap: 15px;
  height: 100%;
  overflow-y: auto;
}

.info-card {
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.4) 0%,
    rgba(25, 55, 125, 0.6) 100%);
  border: 1px solid rgba(0, 149, 255, 0.2);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(0, 254, 255, 0.1),
    transparent);
  transition: left 0.6s ease;
}

.info-card:hover::before {
  left: 100%;
}

.info-card:hover {
  border-color: rgba(0, 149, 255, 0.4);
  box-shadow: 0 0 20px rgba(0, 149, 255, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 149, 255, 0.2);
}

.card-icon {
  font-size: 16px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #00feff;
}

.card-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
}

.label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 15px;
}

.value {
  color: #00feff;
  font-weight: 500;
  font-size: 15px;
}

.value.highlight {
  color: #00ff88;
  font-weight: 600;
  text-shadow: 0 0 5px rgba(0, 255, 136, 0.3);
}

.value.occupancy {
  color: #ffa726;
  font-weight: 600;
  text-shadow: 0 0 5px rgba(255, 167, 38, 0.3);
}

.value.positive-change {
  color: #00ff88;
  font-weight: 600;
}

.value.negative-change {
  color: #ff6b6b;
  font-weight: 600;
}

.value.no-change {
  color: rgba(255, 255, 255, 0.8);
}

/* 自定义滚动条 */
.info-cards::-webkit-scrollbar {
  width: 6px;
}

.info-cards::-webkit-scrollbar-track {
  background: rgba(20, 50, 120, 0.3);
  border-radius: 3px;
}

.info-cards::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #00feff, rgba(0, 149, 255, 0.8));
  border-radius: 3px;
  box-shadow: inset 0 0 3px rgba(0, 254, 255, 0.3);
}

.info-cards::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #00feff, #0095ff);
}
</style>
