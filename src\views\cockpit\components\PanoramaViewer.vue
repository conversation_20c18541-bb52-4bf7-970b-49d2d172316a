<template>
  <div class="panorama-viewer-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p class="loading-text">{{ loadingText }}</p>
    </div>
    
    <!-- 错误状态 -->
    <div v-if="error" class="error-overlay">
      <el-icon class="error-icon"><Warning /></el-icon>
      <p class="error-text">{{ error }}</p>
      <button @click="retryLoad" class="retry-btn">重试</button>
    </div>
    
    <!-- 全景查看器容器 -->
    <div ref="viewerContainer" class="viewer-container" :class="{ 'viewer-hidden': loading || error }"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Viewer } from '@photo-sphere-viewer/core'
import '@photo-sphere-viewer/core/index.css'
import { Warning } from "@element-plus/icons-vue"

// Props定义
const props = defineProps({
  panoramaUrl: {
    type: String,
    required: true,
    default: ''
  }
})

// 响应式数据
const viewerContainer = ref(null)
const viewer = ref(null)
const loading = ref(false)
const error = ref('')
const loadingText = ref('正在加载全景图片...')

// 默认配置
const defaultOptions = {
  navbar: [
    'zoom',
    'move',
    'fullscreen'
  ],
  defaultZoomLvl: 50,
  minFov: 30,
  maxFov: 90,
  mousewheel: true,
  mousemove: true,
  keyboard: 'fullscreen',
  // 启用XMP数据读取，用于自动处理裁剪全景图
  useXmpData: true,
  lang: {
    zoom: '缩放',
    zoomOut: '缩小',
    zoomIn: '放大',
    moveUp: '向上',
    moveDown: '向下',
    moveLeft: '向左',
    moveRight: '向右',
    fullscreen: '全屏',
    loading: '加载中...',
    loadError: '全景图片加载失败'
  }
}

// 初始化全景查看器
const initViewer = async () => {
  if (!props.panoramaUrl || !viewerContainer.value) {
    return
  }

  try {
    loading.value = true
    error.value = ''
    loadingText.value = '正在初始化全景查看器...'

    // 销毁现有的查看器实例
    if (viewer.value) {
      viewer.value.destroy()
      viewer.value = null
    }

    await nextTick()

    // 使用默认配置选项
    const config = {
      ...defaultOptions,
      container: viewerContainer.value,
      panorama: props.panoramaUrl,
      // 使用函数来自动处理非标准比例的图片
      panoData: (image) => {
        console.log('PanoramaViewer: 自动检测图片尺寸', { width: image.width, height: image.height })

        // 计算图片的宽高比
        const aspectRatio = image.width / image.height

        // 如果图片已经是2:1比例，直接使用
        if (Math.abs(aspectRatio - 2) < 0.1) {
          return {
            fullWidth: image.width,
            fullHeight: image.height,
            croppedWidth: image.width,
            croppedHeight: image.height,
            croppedX: 0,
            croppedY: 0,
          }
        }

        // 对于非2:1比例的图片，按照文档中的默认算法处理
        const fullWidth = Math.max(image.width, image.height * 2)
        const fullHeight = Math.round(fullWidth / 2)
        const croppedX = Math.round((fullWidth - image.width) / 2)
        const croppedY = Math.round((fullHeight - image.height) / 2)

        const panoData = {
          fullWidth: fullWidth,
          fullHeight: fullHeight,
          croppedWidth: image.width,
          croppedHeight: image.height,
          croppedX: croppedX,
          croppedY: croppedY,
        }

        console.log('PanoramaViewer: 自动生成全景数据', panoData)
        return panoData
      }
    }

    console.log('PanoramaViewer: 初始化配置', config)

    // 创建新的查看器实例
    viewer.value = new Viewer(config)

    // 监听加载事件
    viewer.value.addEventListener('ready', () => {
      console.log('PanoramaViewer: 全景图片加载完成')
      loading.value = false
    })

    viewer.value.addEventListener('panorama-loaded', () => {
      console.log('PanoramaViewer: 全景数据加载完成')
      loading.value = false
    })

    // 监听错误事件
    viewer.value.addEventListener('panorama-load-error', (err) => {
      console.error('PanoramaViewer: 全景图片加载失败', err)
      error.value = '全景图片加载失败，请检查图片路径或网络连接'
      loading.value = false
    })

  } catch (err) {
    console.error('PanoramaViewer: 初始化失败', err)
    error.value = '全景查看器初始化失败'
    loading.value = false
  }
}

// 重试加载
const retryLoad = () => {
  error.value = ''
  initViewer()
}

// 销毁查看器
const destroyViewer = () => {
  if (viewer.value) {
    try {
      viewer.value.destroy()
      viewer.value = null
      console.log('PanoramaViewer: 查看器已销毁')
    } catch (err) {
      console.error('PanoramaViewer: 销毁查看器时出错', err)
    }
  }
}

// 监听panoramaUrl变化
watch(() => props.panoramaUrl, (newUrl) => {
  if (newUrl) {
    initViewer()
  }
}, { immediate: false })

// 组件挂载
onMounted(() => {
  if (props.panoramaUrl) {
    initViewer()
  }
})

// 组件卸载
onUnmounted(() => {
  destroyViewer()
})

// 暴露方法给父组件
defineExpose({
  viewer,
  retryLoad,
  destroyViewer
})
</script>

<style scoped>
.panorama-viewer-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.8) 0%,
    rgba(25, 55, 125, 0.9) 100%);
}

.viewer-container {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.viewer-hidden {
  opacity: 0;
  pointer-events: none;
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(20, 50, 120, 0.9);
  z-index: 10;
  border-radius: 8px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 254, 255, 0.3);
  border-top: 3px solid #00feff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

.loading-text {
  color: #00feff;
  font-size: 14px;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(20, 50, 120, 0.9);
  z-index: 10;
  border-radius: 8px;
  text-align: center;
  padding: 20px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.error-text {
  color: #ff6b6b;
  font-size: 14px;
  margin: 0 0 15px 0;
  line-height: 1.5;
}

.retry-btn {
  padding: 8px 20px;
  background: linear-gradient(135deg, #00feff 0%, #0095ff 100%);
  border: none;
  border-radius: 4px;
  color: #fff;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: linear-gradient(135deg, #0095ff 0%, #00feff 100%);
  transform: translateY(-1px);
}

/* 全景查看器内部样式覆盖 */
:deep(.psv-container) {
  border-radius: 8px;
}

:deep(.psv-navbar) {
  background: rgba(20, 50, 120, 0.8) !important;
  border-top: 1px solid rgba(0, 149, 255, 0.3) !important;
}

:deep(.psv-button) {
  color: #00feff !important;
}

:deep(.psv-button:hover) {
  background: rgba(0, 254, 255, 0.2) !important;
}

:deep(.psv-loader-container) {
  background: rgba(20, 50, 120, 0.9) !important;
}

:deep(.psv-loader-text) {
  color: #00feff !important;
}
</style>
