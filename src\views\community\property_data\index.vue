<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="组别" prop="communityGroupId" label-width="170">
        <el-select v-model="queryParams.communityGroupId" placeholder="请选择小区组别" clearable style="width: 200px">
          <el-option v-for="dict in communitygroup" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="小区名称" prop="communityId" label-width="170">
        <el-select v-model="queryParams.communityId" placeholder="请选择小区" clearable style="width: 200px">
          <el-option v-for="dict in communitylist" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="成熟小区" prop="isMature" label-width="170">
        <el-select v-model="queryParams.isMature" placeholder="请选择是否成熟小区" clearable style="width: 130px">
          <el-option v-for="dict in commonstatus" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="数据更新时间" prop="lastUpdateTime" label-width="170">
        <el-input v-model="queryParams.lastUpdateTime" placeholder="请输入数据更新时间" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['community:property_data:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['community:property_data:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['community:property_data:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport" v-hasPermi="['community:property_data:import']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['community:property_data:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="property_dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="80" align="center" />
      <el-table-column label="序列" align="center" width="80">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="主键" align="center" prop="id" v-if="false" />
      <el-table-column label="组别" align="center" prop="communityGroupId" width="100">
        <template #default="scope"> <dict-tag :options="communitygroup" :value="scope.row.communityGroupId" /> </template>
      </el-table-column>
      <el-table-column label="小区名称" align="center" prop="communityId" width="100">
        <template #default="scope"> <dict-tag :options="communitylist" :value="scope.row.communityId" /> </template>
      </el-table-column>
      <el-table-column label="成熟小区" align="center" prop="isMature" width="100">
        <template #default="scope"> <dict-tag :options="commonstatus" :value="scope.row.isMature" /> </template>
      </el-table-column>
      <el-table-column label="总栋数" align="center" prop="totalBuildings" width="100" />
      <el-table-column label="住宅总户数，建设总套数" align="center" prop="residence" width="170" />
      <el-table-column label="常住总户数" align="center" prop="permanent" width="130" />
      <el-table-column label="未装修户数" align="center" prop="noDecoration" width="130" />
      <el-table-column label="已装修不常住户数" align="center" prop="isDecoration" width="170" />
      <el-table-column label="常住总人数" align="center" prop="permanentPeople" width="130" />
      <el-table-column label="常住总人数变量" align="center" prop="permanentPeopleVariable" width="170" />
      <el-table-column label="常住总户数变量" align="center" prop="permanentVariable" width="170" />
      <el-table-column label="入住率(%)" align="center" prop="occupancyRate" width="100" />
      <el-table-column label="数据更新时间" align="center" prop="lastUpdateTime" width="130">
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastUpdateTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="130" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['community:property_data:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['community:property_data:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改常住人口数据对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="property_dataRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="小区" prop="communityId" label-width="170">
              <el-select v-model="form.communityId" placeholder="请选择小区" style="width: 280px">
                <el-option
                  v-for="dict in communitylist"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="住宅总户数，建设总套数" prop="residence" label-width="170">
              <el-input v-model="form.residence" placeholder="请输入住宅总户数，建设总套数" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="常住总户数" prop="permanent" label-width="170">
              <el-input v-model="form.permanent" placeholder="请输入常住总户数" style="width: 280px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="未装修户数" prop="noDecoration" label-width="170">
              <el-input v-model="form.noDecoration" placeholder="请输入未装修户数" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="已装修不常住户数" prop="isDecoration" label-width="170">
              <el-input v-model="form.isDecoration" placeholder="请输入已装修不常住户数" style="width: 280px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="常住总人数" prop="permanentPeople" label-width="170">
              <el-input v-model="form.permanentPeople" placeholder="请输入常住总人数" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="常住总人数变量" prop="permanentPeopleVariable" label-width="170">
              <el-input v-model="form.permanentPeopleVariable" placeholder="请输入常住总人数变量" style="width: 280px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="常住总户数变量" prop="permanentVariable" label-width="170">
              <el-input v-model="form.permanentVariable" placeholder="请输入常住总户数变量" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入住率" prop="occupancyRate" label-width="170">
              <el-input v-model="form.occupancyRate" placeholder="请输入入住率" style="width: 280px" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip"><el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据</div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="property_data">
import { getToken } from "@/utils/auth";
import {
  listPropertyData,
  getPropertyData,
  delPropertyData,
  addPropertyData,
  updatePropertyData,
} from "@/api/community/property_data";
const { proxy } = getCurrentInstance();
const { communitygroup, commonstatus, communitylist } = proxy.useDict("communitygroup", "commonstatus", "communitylist");

const property_dataList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const daterangeCreateTime = ref([]);
const daterangeUpdateTime = ref([]);
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    communityGroupName: null,
    communityName: null,
    isMature: null,
  },
  rules: ref({}),
});
const { queryParams, form, rules } = toRefs(data);

/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层
  open: false,
  // 弹出层标题（导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/community/property_data/importData",
}); /** 查询常住人口数据列表 */
function getList() {
  loading.value = true;
  listPropertyData(queryParams.value).then((response) => {
    property_dataList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}
// 取消按钮
function cancel() {
  open.value = false;
  reset();
}
// 表单重置
function reset() {
  form.value = {
    id: null,
    communityId: null,
    residence: null,
    permanent: null,
    noDecoration: null,
    isDecoration: null,
    permanentPeople: null,
    permanentPeopleVariable: null,
    permanentVariable: null,
    occupancyRate: null,
    remark: null,
  };
  proxy.resetForm("property_dataRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加常住人口数据";
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getPropertyData(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改常住人口数据";
  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["property_dataRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updatePropertyData(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addPropertyData(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm("是否确认删除选中的数据，此次将删除" + _ids.length + "条数据？")
    .then(function () {
      return delPropertyData(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "community/property_data/export",
    {
      ...queryParams.value,
    },
    `property_data_${new Date().getTime()}.xlsx`
  );
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "常住人口数据导入";
  upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download("community/property_data/downTemplate", {}, `user_template_${new Date().getTime()}.xlsx`);
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}
getList();
</script>
