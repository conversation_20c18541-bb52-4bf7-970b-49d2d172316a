import request from "@/utils/request";

// 查询楼宇列表
export function listBuilding(query) {
  return request({
    url: "/building/building/list",
    method: "get",
    params: query,
  });
}

// 查询楼宇基本信息
export function getBuilding(buildingId) {
  return request({
    url: "/building/building/" + buildingId,
    method: "get",
  });
}

// 查询楼宇企业列表
export function listBuildingCompany(query) {
  return request({
    url: "/enterprise/enterprise/list",
    method: "get",
    params: query,
  });
}

// 获取小区列表
export function listCommunity(query) {
  return request({
    url: "/community/community/list",
    method: "get",
    params: query,
  });
}

// 获取小区基本信息
export function getCommunity(id) {
  return request({
    url: "/community/community/" + id,
    method: "get",
  });
}

// ==================== 大屏统计API接口 ====================

// 统计楼宇、企业、小区基础数据
export function getBuildingStatis() {
  return request({
    url: "/building/building/statis",
    method: "get",
  });
}

// 企业数量以及增长率
export function getEnterprisesGrowth() {
  return request({
    url: "/Statis/enterprisesGrowth",
    method: "get",
  });
}

// 税收及增长率
export function getTaxationGrowth() {
  return request({
    url: "/Statis/taxationGrowth",
    method: "get",
  });
}

// 小区人口数量及增长率
export function getPopulationGrowth() {
  return request({
    url: "/Statis/populationGrowth",
    method: "get",
  });
}

// 公寓人口数量及增长率
export function getApartmentGrowth() {
  return request({
    url: "/Statis/apartmentnGrowth",
    method: "get",
  });
}

// 楼栋去化率统计
export function getDeConversion() {
  return request({
    url: "/Statis/deConversion",
    method: "get",
  });
}

// 入驻企业分布
export function getEnterpriseDistribution() {
  return request({
    url: "/Statis/enterpriseDistribution",
    method: "get",
  });
}

// 小区居住分布
export function getCommunityDistribution() {
  return request({
    url: "/Statis/communityDistribution",
    method: "get",
  });
}

// 公寓居住分布
export function getApartmentDistribution() {
  return request({
    url: "/Statis/apartmentDistribution",
    method: "get",
  });
}

// 楼栋企业增长趋势
export function getBuildingGrowthTrend() {
  return request({
    url: "/Statis/buildingGrowthtrend",
    method: "get",
  });
}

// 本年月度用电趋势
export function getElectricityTrendForYear() {
  return request({
    url: "/Statis/electricityTrendForYear",
    method: "get",
  });
}

// 居民用电趋势
export function getResidentElectricityTrend() {
  return request({
    url: "/Statis/residentElectricityTrend",
    method: "get",
  });
}

// 移动用户数趋势
export function getMobileTrend() {
  return request({
    url: "/Statis/mobileTrend",
    method: "get",
  });
}