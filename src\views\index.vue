<template>
  <div class="app-container home">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="24" :lg="24">
        <el-card>
          <!-- 快捷入口 -->
          <div class="quick-access">
            <h3 class="section-title">
              <el-icon><Menu /></el-icon>
              快捷入口
            </h3>
            <div class="quick-links">
              <div
                v-for="(link, index) in quickAccessLinks"
                :key="index"
                class="quick-link-item"
                :class="[`priority-${link.priority}`, `color-${link.color}`]"
                @click="navigateTo(link.url)"
              >
                <div class="link-icon" :class="`icon-${link.color}`">
                  <el-icon>
                    <component :is="link.icon" />
                  </el-icon>
                </div>
                <div class="link-content">
                  <div class="link-title">{{ link.name }}</div>
                  <div class="link-desc">{{ link.desc }}</div>
                </div>
                <div class="link-arrow">
                  <el-icon><ArrowRight /></el-icon>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Index">
import { useRouter } from "vue-router";
import { Menu, OfficeBuilding, HomeFilled, Shop, DataAnalysis, ArrowRight } from "@element-plus/icons-vue";

const router = useRouter();

// 使用对象形式管理快捷入口数据
const quickAccessLinks = [
  {
    name: "楼宇管理",
    desc: "管理楼宇基本信息、企业入驻等",
    url: "/building/building",
    icon: OfficeBuilding,
    priority: 1,
    color: "primary",
  },
  {
    name: "小区管理",
    desc: "管理小区基本信息、物业数据等",
    url: "/xiaoqu/community",
    icon: HomeFilled,
    priority: 1,
    color: "success",
  },
  {
    name: "企业管理",
    desc: "管理企业信息、经营状态等",
    url: "/enterprise/enterprise",
    icon: Shop,
    priority: 1,
    color: "warning",
  },
  {
    name: "常驻人口数据管理",
    desc: "管理小区常驻人口统计数据",
    url: "/xiaoqu/property_data",
    icon: DataAnalysis,
    priority: 2,
    color: "info",
  },
  {
    name: "楼栋租控图",
    desc: "管理楼栋单元信息、租控状态等",
    url: "/rental-control",
    icon: OfficeBuilding,
    priority: 1,
    color: "primary",
  },
];

// 导航到指定页面
const navigateTo = (url) => {
  router.push(url);
};
</script>

<style scoped lang="scss">
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }
  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }

  // 快捷入口样式
  .quick-access {
    margin-top: 20px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 20px;

      .el-icon {
        color: #409eff;
      }
    }

    .quick-links {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;
    }

    .quick-link-item {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.1);
        transform: translateY(-2px);
      }

      &.priority-1 {
        border-left: 4px solid var(--border-color);
      }

      &.priority-2 {
        border-left: 4px solid var(--border-color);
      }

      // 颜色主题
      &.color-primary {
        --border-color: #409eff;
        --icon-bg-start: #409eff;
        --icon-bg-end: #66b1ff;
      }

      &.color-success {
        --border-color: #67c23a;
        --icon-bg-start: #67c23a;
        --icon-bg-end: #85ce61;
      }

      &.color-warning {
        --border-color: #e6a23c;
        --icon-bg-start: #e6a23c;
        --icon-bg-end: #ebb563;
      }

      &.color-info {
        --border-color: #909399;
        --icon-bg-start: #909399;
        --icon-bg-end: #a6a9ad;
      }

      .link-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        background: linear-gradient(135deg, var(--icon-bg-start), var(--icon-bg-end));

        .el-icon {
          font-size: 24px;
          color: white;
        }
      }

      .link-content {
        flex: 1;

        .link-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }

        .link-desc {
          font-size: 14px;
          color: #909399;
          line-height: 1.4;
        }
      }

      .link-arrow {
        .el-icon {
          font-size: 16px;
          color: #c0c4cc;
          transition: all 0.3s ease;
        }
      }

      &:hover .link-arrow .el-icon {
        color: #409eff;
        transform: translateX(4px);
      }
    }
  }
}
</style>
