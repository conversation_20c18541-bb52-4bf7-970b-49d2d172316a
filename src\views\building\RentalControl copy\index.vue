<template>
  <div class="p-6 absolute inset-0 flex flex-col">
    <!-- 楼宇选择区域 -->
    <div class="mb-5 p-5 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border border-gray-300">
      <div class="flex items-center justify-between gap-5">
        <div class="flex items-center gap-4">
          <!-- 标题 -->
          <div class="flex items-center flex-shrink-0">
            <span class="text-xl mr-2">🏢</span>
            <span class="text-lg font-semibold text-gray-700">楼宇租控管理</span>
          </div>
          <!-- 下拉框 -->
          <div class="flex-shrink-0 w-70">
            <el-select
              v-model="queryParams.selectedBuildingId"
              placeholder="请选择楼宇"
              filterable
              clearable
              @change="handleBuildingChange"
              :loading="buildingLoading"
            >
              <el-option v-for="building in buildingOptions" :key="building.id" :label="building.name" :value="building.id" />
            </el-select>
          </div>
          <!-- 按钮 -->
          <div class="flex-shrink-0">
            <el-button type="primary" icon="Refresh" @click="refreshBuildingData" :loading="loading"> 刷新数据 </el-button>
          </div>
        </div>

        <!-- 右侧：楼栋信息和操作按钮 -->
        <div class="flex items-center gap-4" v-if="queryParams.selectedBuildingId">
          <span class="text-base font-semibold text-gray-700">{{ selectedBuildingName }}</span>
          <span class="text-sm text-gray-500">共 {{ totalUnitsCount }} 个单元</span>

          <!-- 选择状态指示器和操作按钮 -->
          <div v-if="selectedUnits.size > 0" class="flex items-center gap-2">
            <el-tag type="info" size="small"> 已选择 {{ selectedUnits.size }} 个单元 </el-tag>
            <el-button size="small" type="primary" icon="Edit" @click="showEnterpriseDialog"> 编辑入驻企业 </el-button>
            <el-button size="small" type="warning" icon="Close" @click="clearSelection"> 清除选择 </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 楼层布局图区域 -->
    <div
      v-if="queryParams.selectedBuildingId"
      class="flex-1 bg-white rounded-lg border border-gray-300 overflow-hidden flex flex-col"
    >
      <!-- 楼层和单元整体显示 -->
      <div class="flex-1 overflow-y-auto scrollbar-custom" v-loading="loading">
        <SelectionArea
          class="selection-container"
          :options="{
            selectables: '.unit-cell',
            boundaries: ['.scrollbar-custom'],
          }"
          :onMove="onSelectionMove"
          :onStart="onSelectionStart"
        >
          <div class="flex flex-col">
            <div v-for="floor in floorList" :key="floor.id" class="flex border-b border-gray-300 last:border-b-0">
              <!-- 楼层信息 -->
              <div class="w-48 lg:w-52 p-4 bg-gray-50 border-r border-gray-300 flex flex-col justify-start flex-shrink-0">
                <div class="text-lg font-semibold text-gray-700 mb-2">{{ floor.floorNumber }}F</div>
                <div class="flex flex-col gap-1">
                  <div class="flex gap-1 flex-wrap">
                    <el-tag :type="floor.status === '交付' ? 'success' : 'warning'" size="small">
                      {{ floor.status }}
                    </el-tag>
                    <el-tag v-if="floor.isFireFloor" type="danger" size="small">防火层</el-tag>
                  </div>
                  <div class="text-xs text-gray-500">{{ floor.units?.length || 0 }}单元</div>
                </div>
              </div>

              <!-- 单元网格 -->
              <div class="flex-1 p-4 bg-white overflow-hidden">
                <div class="flex flex-wrap gap-2 min-h-[100px]">
                  <el-tooltip
                    v-for="(unit, index) in floor.units"
                    :key="`${floor.id}-${unit.id || index}`"
                    :content="getUnitTooltip(unit)"
                    placement="top"
                    :show-after="300"
                    :hide-after="100"
                    effect="dark"
                    :popper-style="{ maxWidth: '300px', whiteSpace: 'pre-line' }"
                  >
                    <div
                      :data-key="`${floor.id}-${unit.id || index}`"
                      class="unit-cell w-[100px] h-[50px] border-2 rounded flex flex-col justify-center items-center cursor-pointer text-[10px] text-center relative overflow-hidden flex-shrink-0"
                      :class="getUnitCellClass(unit, `${floor.id}-${unit.id || index}`)"
                      :style="getUnitCellStyle(unit)"
                    >
                      <!-- 企业名称显示 -->
                      <div v-if="unit.enterpriseName" class="font-semibold text-[11px] leading-tight px-1 truncate w-full">
                        {{ unit.enterpriseName }}
                      </div>
                      <!-- 空白单元格 -->
                      <div v-else class="w-full h-full flex items-center justify-center text-gray-400">
                        <span class="text-[8px]">{{ unit.unitNumber }}</span>
                      </div>
                    </div>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>
        </SelectionArea>
      </div>
    </div>

    <!-- 未选择楼宇时的提示 -->
    <div v-else class="flex items-center justify-center h-96 text-gray-500 mt-12">
      <el-empty description="请先选择楼宇以查看租控信息" />
    </div>

    <!-- 企业信息录入弹窗 -->
    <el-dialog :title="enterpriseDialogTitle" v-model="enterpriseDialogOpen" width="1200px" append-to-body>
      <!-- 选中单元区域 -->
      <div class="mb-8">
        <div class="flex items-center gap-3 mb-4 pb-3 border-b border-gray-200">
          <div class="text-lg font-semibold text-gray-800">选中单元</div>
          <div class="px-2 py-1 bg-blue-100 text-blue-700 text-sm font-medium rounded">{{ selectedUnitsInfo.length }} 个</div>
        </div>
        <div class="flex items-start gap-4">
          <div class="text-sm font-medium text-gray-600 whitespace-nowrap pt-1">已选择：</div>
          <div class="flex flex-wrap gap-2 flex-1">
            <el-tag v-for="unit in selectedUnitsInfo" :key="unit.key" size="default" type="info" effect="plain">
              {{ unit.floorNumber }}F-{{ unit.unitNumber }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 企业选择区域 -->
      <div>
        <div class="flex items-center justify-between mb-4 pb-3 border-b border-gray-200">
          <div class="text-lg font-semibold text-gray-800">选择企业</div>
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-600">代表颜色：</span>
            <el-color-picker
              v-model="selectedEnterpriseColor"
              :predefine="predefineColors"
              size="default"
              :disabled="!selectedEnterprise"
              @change="handleColorChange"
            />
          </div>
        </div>

        <!-- 企业搜索 -->
        <el-form :model="enterpriseSearchForm" :inline="true" class="search-form">
          <el-form-item label="企业名称">
            <el-input
              v-model="enterpriseSearchForm.name"
              placeholder="请输入企业名称"
              clearable
              @input="handleEnterpriseSearch"
              style="width: 250px"
            />
          </el-form-item>
          <el-form-item label="统一社会信用代码">
            <el-input
              v-model="enterpriseSearchForm.uscCode"
              placeholder="请输入统一社会信用代码"
              clearable
              @input="handleEnterpriseSearch"
              style="width: 250px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleEnterpriseSearch">搜索</el-button>
            <el-button @click="resetEnterpriseSearch">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 企业列表 -->
        <el-table :data="enterpriseOptions" height="300" v-loading="enterpriseLoading" class="enterprise-table">
          <el-table-column label="选择" width="80" align="center">
            <template #default="{ row }">
              <el-radio v-model="selectedEnterpriseId" :label="row.id" @change="handleEnterpriseRadioChange(row)">
                &nbsp;
              </el-radio>
            </template>
          </el-table-column>
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column label="统一社会信用代码" prop="uscCode" width="200" align="center" show-overflow-tooltip />
          <el-table-column label="企业名称" prop="name" min-width="300" align="center" show-overflow-tooltip />
          <el-table-column label="法人" prop="legalPerson" width="120" align="center" show-overflow-tooltip />
          <el-table-column label="联系电话" prop="concatMobile" width="130" align="center" show-overflow-tooltip />
        </el-table>

        <!-- 分页组件 -->
        <div class="flex justify-center mt-4 pt-4 border-t border-gray-100">
          <el-pagination
            v-model:current-page="enterprisePagination.currentPage"
            v-model:page-size="enterprisePagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="enterprisePagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleEnterpriseSizeChange"
            @current-change="handleEnterprisePageChange"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelEnterpriseDialog">取 消</el-button>
          <el-button type="primary" @click="submitEnterpriseForm" :loading="enterpriseSubmitting" :disabled="!selectedEnterprise">
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { ElMessage } from "element-plus";
import { SelectionArea } from "@viselect/vue";
import { listBuilding } from "@/api/cockpit/index";
import { listEnterprise } from "@/api/enterprise/enterprise";

// 楼宇相关数据
const buildingOptions = ref([]);
const buildingLoading = ref(false);
const selectedBuildingName = ref("");

// 楼层和单元数据
const floorList = ref([]);
const loading = ref(false);

// 查询参数
const queryParams = ref({
  selectedBuildingId: null,
});

// 多选相关数据
const selectedUnits = ref(new Set());

// 企业相关数据
const enterpriseOptions = ref([]);
const enterpriseLoading = ref(false);
const enterpriseDialogOpen = ref(false);
const enterpriseDialogTitle = ref("编辑入驻企业信息");
const enterpriseSubmitting = ref(false);

// 企业表单
const enterpriseForm = ref({
  enterpriseId: null,
  enterpriseName: "",
});

const enterpriseFormRef = ref();
const selectedEnterprise = ref(null);
const selectedEnterpriseId = ref(null);

// 颜色选择相关
const selectedEnterpriseColor = ref('#409EFF');
const predefineColors = ref([
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  '#409EFF',
  '#67C23A',
  '#E6A23C',
  '#F56C6C',
  '#909399'
]);

// 企业搜索表单
const enterpriseSearchForm = ref({
  name: "",
  uscCode: "",
});

// 企业分页数据
const enterprisePagination = ref({
  currentPage: 1,
  pageSize: 5,
  total: 0,
});



// 选项数据
const orientations = ["东", "南", "西", "北", "东南", "东北", "西南", "西北"];
const decorationLevels = ["毛坯", "简装", "精装"];
const rentalStatuses = ["空置", "已出租", "维修中", "预定", "停租"];

// 计算属性
const totalUnitsCount = computed(() => {
  return floorList.value.reduce((total, floor) => {
    return total + (floor.units?.length || 0);
  }, 0);
});

// 选中单元信息
const selectedUnitsInfo = computed(() => {
  const unitsInfo = [];
  selectedUnits.value.forEach((unitKey) => {
    const [floorId, unitId] = unitKey.split("-");
    const floor = floorList.value.find((f) => f.id.toString() === floorId);
    if (floor) {
      const unit = floor.units.find((u) => (u.id || u.unitNumber).toString() === unitId);
      if (unit) {
        unitsInfo.push({
          key: unitKey,
          floorNumber: floor.floorNumber,
          unitNumber: unit.unitNumber,
          floorId: floor.id,
          unitId: unit.id || unit.unitNumber,
        });
      }
    }
  });
  return unitsInfo;
});

// 工具函数
const getUnitTooltip = (unit) => {
  const baseInfo = [
    `单元号: ${unit.unitNumber || "未设置"}`,
    `建筑面积: ${unit.buildingArea || "未设置"}㎡`,
    `实用面积: ${unit.usableArea || "未设置"}㎡`,
    `朝向: ${unit.orientation || "未设置"}`,
    `装修状态: ${unit.decorationLevel || "未设置"}`,
  ];

  if (unit.enterpriseName) {
    return [`企业: ${unit.enterpriseName}`, baseInfo[0], baseInfo[1], baseInfo[2], baseInfo[3], baseInfo[4], `状态: 已租用`].join(
      "\n"
    );
  }

  return [baseInfo[0], baseInfo[1], baseInfo[2], baseInfo[3], baseInfo[4], `状态: 空置`].join("\n");
};

// 获取单元格样式类
const getUnitCellClass = (unit, unitKey) => {
  const isSelected = selectedUnits.value.has(unitKey);

  let baseClass = "border-gray-300 bg-white text-gray-700";

  // 如果绑定了企业且有颜色，使用企业颜色
  if (unit.enterpriseId && unit.enterpriseColor) {
    const rgb = hexToRgb(unit.enterpriseColor);
    const brightness = rgb ? (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000 : 128;
    const textColor = brightness > 128 ? '#000000' : '#ffffff';

    baseClass = `border-2 text-sm font-medium`;
    // 使用内联样式而不是类名
  }

  // 如果被选中，只修改边框样式，保持背景颜色不变
  if (isSelected) {
    baseClass += " selected";
  }

  return baseClass;
};

// 获取单元格内联样式
const getUnitCellStyle = (unit) => {
  if (unit.enterpriseId && unit.enterpriseColor) {
    const rgb = hexToRgb(unit.enterpriseColor);
    const brightness = rgb ? (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000 : 128;
    const textColor = brightness > 128 ? '#000000' : '#ffffff';

    return {
      backgroundColor: unit.enterpriseColor,
      borderColor: unit.enterpriseColor,
      color: textColor
    };
  }
  return {};
};

// ==================== 楼宇相关操作 ====================

/** 获取楼宇列表 */
const getBuildingList = async () => {
  try {
    buildingLoading.value = true;
    const response = await listBuilding({ pageNum: 1, pageSize: 1000 });
    buildingOptions.value = response.rows || [];
  } catch (error) {
    console.error("获取楼宇列表失败:", error);
    ElMessage.error("获取楼宇列表失败");
  } finally {
    buildingLoading.value = false;
  }
};

/** 楼宇选择变化处理 */
const handleBuildingChange = (buildingId) => {
  if (buildingId) {
    const building = buildingOptions.value.find((b) => b.id === buildingId);
    selectedBuildingName.value = building?.name || "";
    loadBuildingData(buildingId);
  } else {
    selectedBuildingName.value = "";
    floorList.value = [];
  }
};

/** 加载楼宇数据 */
const loadBuildingData = async (buildingId) => {
  loading.value = true;
  try {
    // 模拟API调用，实际应该调用真实接口
    await new Promise((resolve) => setTimeout(resolve, 800));

    // 模拟楼层和单元数据
    floorList.value = generateMockFloorData(buildingId);
  } catch (error) {
    console.error("加载楼宇数据失败:", error);
    ElMessage.error("加载楼宇数据失败");
  } finally {
    loading.value = false;
  }
};

/** 生成模拟楼层数据 */
const generateMockFloorData = (buildingId) => {
  const floors = [];
  const floorCount = Math.floor(Math.random() * 20) + 10; // 10-30层

  for (let i = floorCount; i >= 1; i--) {
    const unitCount = Math.floor(Math.random() * 12) + 4; // 4-15个单元
    const floorId = Date.now() + i;
    const units = [];

    for (let j = 1; j <= unitCount; j++) {
      const unitNumber = `${i.toString().padStart(2, "0")}${j.toString().padStart(2, "0")}`;
      units.push({
        id: Date.now() + Math.random(),
        unitNumber,
        buildingArea: Math.floor(Math.random() * 100) + 50,
        usableArea: Math.floor(Math.random() * 80) + 40,
        orientation: orientations[Math.floor(Math.random() * orientations.length)],
        decorationLevel: decorationLevels[Math.floor(Math.random() * decorationLevels.length)],
        rentalStatus: rentalStatuses[Math.floor(Math.random() * rentalStatuses.length)],
        remark: "",
        floorId: floorId, // 添加楼层ID
        enterpriseId: null, // 企业ID
        enterpriseName: null, // 企业名称
      });
    }

    floors.push({
      id: floorId,
      floorNumber: i,
      status: Math.random() > 0.3 ? "交付" : "未交付",
      isFireFloor: i % 10 === 0, // 每10层设为防火层
      remark: i === 1 ? "一楼大厅" : i % 10 === 0 ? "防火层" : "",
      units,
    });
  }

  return floors;
};

/** 刷新楼宇数据 */
const refreshBuildingData = () => {
  if (queryParams.value.selectedBuildingId) {
    loadBuildingData(queryParams.value.selectedBuildingId);
  } else {
    ElMessage.warning("请先选择楼宇");
  }
};

// ==================== 多选相关操作 ====================

/** 选择开始 */
const onSelectionStart = ({ event, selection }) => {
  // 如果不是按住Ctrl/Cmd键，清除之前的选择
  if (!event?.ctrlKey && !event?.metaKey) {
    selection.clearSelection();
    selectedUnits.value.clear();
  }
};

/** 选择移动 */
const onSelectionMove = ({
  store: {
    changed: { added, removed },
  },
}) => {
  // 添加新选中的单元格
  added.forEach((el) => {
    const unitKey = el.getAttribute("data-key");
    if (unitKey) selectedUnits.value.add(unitKey);
  });

  // 移除取消选中的单元格
  removed.forEach((el) => {
    const unitKey = el.getAttribute("data-key");
    if (unitKey) selectedUnits.value.delete(unitKey);
  });
};

/** 清除选择 */
const clearSelection = () => {
  selectedUnits.value.clear();
};

// ==================== 企业相关操作 ====================

/** 获取企业列表 */
const getEnterpriseList = async (params = {}) => {
  try {
    enterpriseLoading.value = true;
    const requestParams = {
      pageNum: enterprisePagination.value.currentPage,
      pageSize: enterprisePagination.value.pageSize,
      ...enterpriseSearchForm.value,
      ...params,
    };

    const response = await listEnterprise(requestParams);
    enterpriseOptions.value = response.rows || [];
    enterprisePagination.value.total = response.total || 0;

    console.log("企业列表:", enterpriseOptions.value);
  } catch (error) {
    console.error("获取企业列表失败:", error);
    ElMessage.error("获取企业列表失败");
  } finally {
    enterpriseLoading.value = false;
  }
};

/** 显示企业绑定弹窗 */
const showEnterpriseDialog = () => {
  enterpriseDialogOpen.value = true;
  enterpriseForm.value = {
    enterpriseId: null,
    enterpriseName: "",
  };
};

/** 企业radio选择变化处理 */
const handleEnterpriseRadioChange = (row) => {
  selectedEnterprise.value = row;
  // 如果企业有保存的颜色，则使用保存的颜色，否则使用默认颜色
  selectedEnterpriseColor.value = row.color || '#409EFF';
};

/** 颜色变化处理 */
const handleColorChange = (color) => {
  selectedEnterpriseColor.value = color;
};



/** 十六进制颜色转RGB */
const hexToRgb = (hex) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

/** 企业搜索处理 */
const handleEnterpriseSearch = () => {
  // 重置到第一页
  enterprisePagination.value.currentPage = 1;
  // 重新获取数据
  getEnterpriseList();
};

/** 重置企业搜索 */
const resetEnterpriseSearch = () => {
  enterpriseSearchForm.value = {
    name: "",
    uscCode: "",
  };
  // 重置到第一页并重新获取数据
  enterprisePagination.value.currentPage = 1;
  getEnterpriseList();
};

/** 企业分页大小变化 */
const handleEnterpriseSizeChange = (size) => {
  enterprisePagination.value.pageSize = size;
  enterprisePagination.value.currentPage = 1;
  getEnterpriseList();
};

/** 企业分页页码变化 */
const handleEnterprisePageChange = (page) => {
  enterprisePagination.value.currentPage = page;
  getEnterpriseList();
};

/** 取消企业绑定弹窗 */
const cancelEnterpriseDialog = () => {
  enterpriseDialogOpen.value = false;
  selectedUnits.value.clear();
  selectedEnterprise.value = null;
  selectedEnterpriseId.value = null;
  selectedEnterpriseColor.value = '#409EFF'; // 重置颜色为默认值

  // 重置搜索表单和分页
  enterpriseSearchForm.value = {
    name: "",
    uscCode: "",
  };
  enterprisePagination.value.currentPage = 1;
};

/** 提交企业绑定表单 */
const submitEnterpriseForm = async () => {
  if (!selectedEnterprise.value) {
    ElMessage.warning("请选择一个企业");
    return;
  }

  if (!selectedEnterpriseColor.value) {
    ElMessage.warning("请选择企业颜色");
    return;
  }

  try {
    enterpriseSubmitting.value = true;

    const { id: enterpriseId, name: enterpriseName } = selectedEnterprise.value;
    const enterpriseColor = selectedEnterpriseColor.value;

    // 准备提交给后端的数据
    const submitData = {
      enterpriseId,
      enterpriseName,
      enterpriseColor,
      units: selectedUnitsInfo.value.map(unitInfo => ({
        floorId: unitInfo.floorId,
        unitId: unitInfo.unitId,
        unitNumber: unitInfo.unitNumber
      }))
    };

    // TODO: 调用后端API保存企业绑定信息和颜色
    console.log('提交给后端的数据:', submitData);

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 更新选中单元格的企业信息（包含颜色）
    selectedUnitsInfo.value.forEach((unitInfo) => {
      const floor = floorList.value.find((f) => f.id === unitInfo.floorId);
      if (floor) {
        const unit = floor.units.find((u) => (u.id || u.unitNumber) === unitInfo.unitId);
        if (unit) {
          unit.enterpriseId = enterpriseId;
          unit.enterpriseName = enterpriseName;
          unit.enterpriseColor = enterpriseColor; // 保存颜色信息
          unit.floorId = floor.id; // 确保单元格有楼层ID
        }
      }
    });

    ElMessage.success(`成功绑定企业：${enterpriseName}`);
    cancelEnterpriseDialog();
  } catch (error) {
    console.error("企业绑定失败:", error);
    ElMessage.error("企业绑定失败");
  } finally {
    enterpriseSubmitting.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  getBuildingList();
  getEnterpriseList();
});
</script>

<style>
/* SelectionArea 容器样式 */
.selection-container {
  user-select: none;
}

/* Viselect 选择区域样式 */
.selection-area {
  background: rgba(46, 115, 252, 0.11);
  border: 1px solid rgba(98, 155, 255, 0.85);
  border-radius: 0.15em;
}
/* 自定义滚动条样式 */
.scrollbar-custom {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.scrollbar-custom::-webkit-scrollbar {
  width: 8px;
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.scrollbar-custom::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 选中状态样式 */
.unit-cell.selected {
  border-color: #3b82f6 !important;
  border-width: 3px !important;
}

/* 企业信息录入弹窗样式优化 */
.search-form .el-form-item__label {
  color: #374151;
  font-weight: 500;
}

.enterprise-table {
  border-radius: 8px;
  overflow: hidden;
}

.enterprise-table .el-table__header-wrapper th {
  background-color: #f9fafb;
  color: #374151;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
}

.enterprise-table .el-table__row:hover > td {
  background-color: #f8fafc;
}

/* 标题区域样式 */
.dialog-section-title {
  position: relative;
}

.dialog-section-title::after {
  content: "";
  position: absolute;
  bottom: -12px;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: 1px;
}
</style>
