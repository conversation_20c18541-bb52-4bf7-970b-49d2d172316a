<template>
  <!-- 楼宇诉求 Tab -->
  <div class="tab-pane">
    <div class="table-section">
      <div class="section-header">
        <span class="section-icon">📋</span>
        <span class="section-title">楼宇诉求列表</span>
        <span class="tenant-count">共 {{ pagination.total }} 条诉求</span>
      </div>

      <div class="table-container">
        <div class="table-wrapper">
          <table class="data-table">
            <thead>
              <tr>
                <th>诉求类型</th>
                <th>诉求详情</th>
                <th>提交时间</th>
                <th>处理部门</th>
                <th>处理状态</th>
                <th>处理时间</th>
                <th>备注</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(issue, index) in paginatedIssues" :key="index">
                <td class="issue-type">{{ issue.type || '-' }}</td>
                <td class="issue-detail">{{ issue.detail || '-' }}</td>
                <td>{{ issue.submitTime || '-' }}</td>
                <td class="department">{{ issue.department || '-' }}</td>
                <td>
                  <span class="status-badge" :class="getStatusClass(issue.status)">
                    {{ issue.status || '-' }}
                  </span>
                </td>
                <td>{{ issue.processTime || '-' }}</td>
                <td class="remark">{{ issue.remark || '-' }}</td>
              </tr>
              <tr v-if="paginatedIssues.length === 0">
                <td colspan="7" class="no-data">暂无诉求数据</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <div class="pagination-info">
          显示第 {{ (pagination.currentPage - 1) * pagination.pageSize + 1 }} -
          {{ Math.min(pagination.currentPage * pagination.pageSize, pagination.total) }} 条，
          共 {{ pagination.total }} 条
        </div>
        <div class="pagination-controls">
          <button
            @click="changePage(pagination.currentPage - 1)"
            :disabled="pagination.currentPage === 1"
            class="pagination-btn"
          >
            上一页
          </button>
          <span class="page-info">
            {{ pagination.currentPage }} / {{ Math.ceil(pagination.total / pagination.pageSize) }}
          </span>
          <button
            @click="changePage(pagination.currentPage + 1)"
            :disabled="pagination.currentPage >= Math.ceil(pagination.total / pagination.pageSize)"
            class="pagination-btn"
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, computed } from 'vue';

const props = defineProps({
  buildingData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits([]);

// 分页状态管理
const pagination = ref({
  currentPage: 1,
  pageSize: 5,
  total: 0
});

// 分页相关计算属性
const paginatedIssues = computed(() => {
  const data = props.buildingData.issues || [];
  pagination.value.total = data.length;
  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize;
  const end = start + pagination.value.pageSize;
  return data.slice(start, end);
});

// 状态样式
const getStatusClass = (status) => {
  switch (status) {
    case '已处理': return 'status-completed';
    case '处理中': return 'status-processing';
    case '待处理': return 'status-pending';
    case '已关闭': return 'status-closed';
    default: return 'status-default';
  }
};

// 分页方法
const changePage = (page) => {
  if (page < 1) return;
  const maxPage = Math.ceil(pagination.value.total / pagination.value.pageSize);
  if (page > maxPage) return;
  pagination.value.currentPage = page;
};
</script>

<style scoped>
/* 表格相关样式 */
.table-section {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  padding: 12px 16px;
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.4) 0%,
    rgba(25, 55, 125, 0.6) 100%);
  border: 1px solid rgba(0, 149, 255, 0.2);
  border-radius: 6px;
}

.section-icon {
  font-size: 18px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #00feff;
}

.tenant-count {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(0, 149, 255, 0.2);
  padding: 4px 12px;
  border-radius: 12px;
  border: 1px solid rgba(0, 149, 255, 0.3);
}

.table-container {
  flex: 1;
  overflow: hidden;
  border: 1px solid rgba(0, 149, 255, 0.2);
  border-radius: 8px;
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.3) 0%,
    rgba(25, 55, 125, 0.5) 100%);
}

.table-wrapper {
  height: 100%;
  overflow: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  min-width: 800px;
}

.data-table th {
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.8) 0%,
    rgba(25, 55, 125, 0.9) 100%);
  color: #00feff;
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  border-bottom: 2px solid rgba(0, 149, 255, 0.3);
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table td {
  padding: 10px 8px;
  border-bottom: 1px solid rgba(0, 149, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  transition: background-color 0.3s ease;
}

.data-table tr:hover td {
  background: rgba(0, 149, 255, 0.1);
}

.data-table .issue-type {
  color: #ff9800;
  font-weight: 600;
}

.data-table .issue-detail {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.data-table .department {
  color: #9c27b0;
  font-weight: 500;
}

.data-table .remark {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-completed {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-processing {
  background: rgba(33, 150, 243, 0.2);
  color: #2196f3;
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.status-pending {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
  border: 1px solid rgba(255, 152, 0, 0.3);
}

.status-closed {
  background: rgba(158, 158, 158, 0.2);
  color: #9e9e9e;
  border: 1px solid rgba(158, 158, 158, 0.3);
}

.status-default {
  background: rgba(158, 158, 158, 0.2);
  color: #9e9e9e;
  border: 1px solid rgba(158, 158, 158, 0.3);
}

.no-data {
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
  padding: 40px;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 16px;
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.4) 0%,
    rgba(25, 55, 125, 0.6) 100%);
  border: 1px solid rgba(0, 149, 255, 0.2);
  border-radius: 6px;
  margin-top: 15px;
}

.pagination-info {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.pagination-btn {
  background: linear-gradient(135deg,
    rgba(0, 149, 255, 0.6) 0%,
    rgba(0, 149, 255, 0.8) 100%);
  border: 1px solid rgba(0, 149, 255, 0.3);
  color: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.pagination-btn:hover:not(:disabled) {
  background: linear-gradient(135deg,
    rgba(0, 149, 255, 0.8) 0%,
    rgba(0, 149, 255, 1) 100%);
  box-shadow: 0 0 8px rgba(0, 149, 255, 0.4);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: #00feff;
  font-weight: 600;
}

/* 滚动条样式 */
.table-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: rgba(20, 50, 120, 0.3);
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #00feff, rgba(0, 149, 255, 0.8));
  border-radius: 4px;
  box-shadow: inset 0 0 3px rgba(0, 254, 255, 0.3);
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #00feff, #0095ff);
}
</style>
