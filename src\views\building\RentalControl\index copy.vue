<template>
  <div class="p-6 absolute inset-0 flex flex-col">
    <!-- 头部 -->
    <div class="mb-5 p-5 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border border-gray-300">
      <div class="flex items-center justify-between gap-5">
        <!-- 左侧：基础功能区 -->
        <div class="flex items-center gap-4">
          <!-- 标题 -->
          <div class="flex items-center flex-shrink-0">
            <span class="text-xl mr-2">🏢</span>
            <span class="text-lg font-semibold text-gray-700">楼宇租控管理</span>
          </div>
          <!-- 楼宇选择 -->
          <div class="flex-shrink-0 w-70">
            <el-select v-model="queryParams.selectedBuildingId" placeholder="请选择楼宇" filterable clearable
              @change="handleBuildingChange" :loading="buildingLoading">
              <el-option v-for="building in buildingOptions" :key="building.id" :label="building.name"
                :value="building.id" />
            </el-select>
          </div>
          <!-- 刷新按钮 -->
          <div class="flex-shrink-0">
            <el-button type="primary" icon="Refresh" @click="refreshBuildingData" :loading="loading"> 刷新数据 </el-button>
          </div>
        </div>

        <!-- 右侧：模式切换和操作按钮 -->
        <div class="flex items-center gap-4">
          <!-- 模式切换 -->
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-600">模式：</span>
            <el-radio-group v-model="currentMode" size="small">
              <el-radio-button value="view">展示模式</el-radio-button>
              <el-radio-button value="edit">编辑模式</el-radio-button>
            </el-radio-group>
          </div>

          <!-- 操作按钮区 -->
          <div class="flex items-center gap-2">
            <!-- 单元编辑按钮 -->
            <el-button size="small" type="primary" icon="Edit" :disabled="currentMode !== 'edit'"
              @click="showEnterpriseDialog">
              编辑单元 ({{ selectedUnits.size }})
            </el-button>
            <!-- 清除选择按钮 -->
            <el-button :disabled="currentMode !== 'edit' || selectedUnits.size === 0" size="small" type="warning"
              icon="Close" @click="clearSelection">
              清除选择
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 租控图 -->
    <div v-if="queryParams.selectedBuildingId"
      class="flex-1 bg-white rounded-lg border border-gray-300 overflow-hidden flex flex-col">
      <!-- 租控图表格区域 -->
      <div class="flex-1 overflow-auto scrollbar-custom" v-loading="loading">
        <SelectionArea ref="selectionAreaRef" class="selection-container" :options="{
          selectables: '.unit-cell',
          boundaries: ['.scrollbar-custom'],
        }" :onMove="onSelectionMove" :onStart="onSelectionStart">
          <div class="flex flex-col">
            <div v-for="floor in floorList" :key="`floor${floor.id}`"
              class="flex border-b border-gray-300 last:border-b-0">
              <!-- 楼层信息 -->
              <div
                class="w-35 bg-gray-50 border-r border-gray-300 flex justify-center items-center flex-shrink-0 relative">
                <span class="text-[20px] font-semibold text-gray-700">{{ floor.name }}F</span>
                <el-tag v-if="floor.deliveryStatus === '未交付'" type="danger" size="small"
                  class="absolute right-2 text-[10px] scale-75 origin-right">
                  {{ floor.deliveryStatus }}
                </el-tag>
              </div>

              <!-- 单元网格 -->
              <div class="flex-1 p-2 bg-white flex items-center">
                <div class="flex gap-2 ">
                  <template v-for="(item, index) in floor.rentControls"
                    :key="`floor${floor.id}-unit${item.floorUnitId || item.id || index}`">
                    <!-- 分组渲染 -->
                    <div v-if="item.group" class="flex gap-2 relative h-[50px]">
                      <!-- 子单元格 -->
                      <div v-for="unit in item.children" :key="`${floor.id}-${unit.floorUnitId}`"
                        class="unit-cell w-[80px] h-[50px] border-[2px] border-[#d1d5dc] rounded-lg">
                      </div>
                      <!-- 合并单元格 -->
                      <el-tooltip :content="getTooltip(item)" placement="top" :show-after="300" :hide-after="100"
                        effect="dark" :popper-style="{ maxWidth: '300px', whiteSpace: 'pre-line' }">
                        <div
                          class="absolute top-0 left-0 w-full h-full border-[2px] border-[#d1d5dc] rounded-lg flex flex-col justify-center items-center cursor-pointer text-[12px] text-center overflow-hidden flex-shrink-0 text-black font-bold"
                          :style="getRentControlCellStyle(item)">
                          {{ item.enterpriseName }}
                        </div>
                      </el-tooltip>
                    </div>

                    <!-- 普通单元渲染 -->
                    <el-tooltip :content="getTooltip(item)" placement="top" :show-after="300" :hide-after="100"
                      effect="dark" :popper-style="{ maxWidth: '300px', whiteSpace: 'pre-line' }">
                      <div :data-key="`${floor.id}-${item.floorUnitId}`"
                        class="unit-cell w-[80px] h-[50px] border-[2px] border-[#d1d5dc] rounded-lg flex flex-col justify-center items-center cursor-pointer text-[12px] font-bold text-center relative overflow-hidden flex-shrink-0 text-black"
                        :style="getRentControlCellStyle(item)">
                        <!-- 企业名称显示 -->
                        <div v-if="item.enterpriseName"
                          class=" text-[13px] font-bold leading-tight px-1 truncate w-full">
                          {{ item.enterpriseName }}
                        </div>
                        <!-- 空白单元格 -->
                        <div v-else class="w-full h-full flex items-center justify-center">
                          <span class=" text-[13px] font-bold">{{ `${item.floorName}F-${item.floorUnitId}`
                          }}</span>
                        </div>
                      </div>
                    </el-tooltip>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </SelectionArea>
      </div>
    </div>

    <!-- 未选择楼宇时的提示 -->
    <div v-else class="flex items-center justify-center h-96 text-gray-500 mt-12">
      <el-empty description="请先选择楼宇以查看租控信息" />
    </div>

    <!-- 企业信息录入弹窗 -->
    <el-dialog title="编辑入驻企业信息" v-model="enterpriseDialogOpen" width="600px" append-to-body
      :close-on-click-modal="false">
      <!-- 企业信息表单 -->
      <el-form :model="enterpriseForm" ref="enterpriseFormRef" :rules="enterpriseFormRules" label-width="120px"
        class="enterprise-form">
        <!-- 选中单元信息 -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="选中单元">
              <div class="flex flex-wrap gap-2">
                <el-tag v-for="unit in selectedUnitsInfo" :key="unit.id" type="primary" size="default" class="unit-tag">
                  {{ unit.floorName }}F-{{ unit.floorUnitName }}
                </el-tag>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 类型选择 -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="类型选择" prop="unitType">
              <el-select v-model="enterpriseForm.unitType" placeholder="请选择类型" style="width: 400px"
                @change="handleTypeChange">
                <el-option v-for="dict in fun_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 企业名称 -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="企业名称" prop="enterpriseName">
              <div>
                <el-autocomplete v-model="enterpriseForm.enterpriseName" :fetch-suggestions="queryEnterpriseSearch"
                  placeholder="请输入企业名称进行搜索" clearable style="width: 400px" @select="handleEnterpriseSelect"
                  @clear="handleEnterpriseClear" @input="handleEnterpriseInput" value-key="name"
                  class="enterprise-autocomplete" :disabled="!showEnterpriseFields">
                  <template #default="{ item }">
                    <div class="enterprise-suggestion-item">
                      <div class="enterprise-name">{{ item.name }}</div>
                      <div class="enterprise-code">{{ item.uscCode }}</div>
                    </div>
                  </template>
                </el-autocomplete>

                <!-- 企业详细信息显示容器 - 默认展示 -->
                <div class="enterprise-info-display">
                  <!-- 有选中企业时显示详细信息 -->
                  <div v-if="selectedEnterprise" class="enterprise-details">
                    <div class="info-title">企业详细信息</div>
                    <div class="info-grid">
                      <div class="info-item">
                        <span class="info-label">企业名称：</span>
                        <span class="info-value">{{ selectedEnterprise.name }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">统一社会信用代码：</span>
                        <span class="info-value">{{ selectedEnterprise.uscCode }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">法人：</span>
                        <span class="info-value">{{ selectedEnterprise.legalPerson || "-" }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">联系电话：</span>
                        <span class="info-value">{{ selectedEnterprise.concatMobile || "-" }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- 默认状态或暂无企业信息 -->
                  <div v-else class="no-enterprise">
                    <el-empty :description="enterpriseForm.enterpriseName ? '暂无企业信息' : '请输入企业名称进行搜索'"
                      :image-size="60" />
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelEnterpriseDialog">取 消</el-button>
          <el-button type="primary" @click="submitEnterpriseForm" :loading="enterpriseSubmitting"> 确 定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, watchEffect, getCurrentInstance } from "vue";
import { ElMessage } from "element-plus";
import { SelectionArea } from "@viselect/vue";
import { listBuilding } from "@/api/cockpit/index";
import { listEnterprise } from "@/api/enterprise/enterprise";
import { getBuildingRentControl } from "@/api/building/rent_control";

// 获取代理实例
const { proxy } = getCurrentInstance();

// 楼宇相关数据
const buildingOptions = ref([]);
const buildingLoading = ref(false);

// 楼层和单元数据
const originalFloorList = ref([]); // 原始数据
const groupedFloorList = ref([]); // 分组数据
const floorList = ref([]); // 当前显示的数据
const loading = ref(false);

// 查询参数
const queryParams = ref({
  selectedBuildingId: null,
});

// 多选相关数据
const selectionAreaRef = ref(null);
const selectedUnits = ref(new Set());

// 模式切换
const currentMode = ref("view"); // 默认展示模式

// 功能类型字典
const { fun_type } = proxy.useDict("fun_type");

// 企业相关数据
const enterpriseDialogOpen = ref(false);
const enterpriseSubmitting = ref(false);

// 企业表单
const enterpriseForm = ref({
  enterpriseId: null,
  enterpriseName: "",
  unitType: null, // 新增类型字段
});

const enterpriseFormRef = ref();
const selectedEnterprise = ref(null);
const selectedEnterpriseId = ref(null);

// 企业表单验证规则
const enterpriseFormRules = ref({
  unitType: [{ required: true, message: "请选择类型", trigger: "change" }],
  // 企业名称不设置必填验证，因为有些类型不需要企业信息
});

// 是否显示企业相关字段（企业名称和颜色选择）
const showEnterpriseFields = computed(() => {
  const unitType = enterpriseForm.value.unitType;
  // 当类型为"已出租"(1)或"在谈客户"(3)时显示企业字段
  return unitType === '1' || unitType === '3';
});

// 选中单元信息
const selectedUnitsInfo = computed(() => {
  const unitsInfo = [];
  selectedUnits.value.forEach((unitKey) => {
    const [floorId, unitId] = unitKey.split("-");
    const floor = originalFloorList.value.find((f) => f.id.toString() === floorId);
    if (floor) {
      const unit = floor.rentControls?.find((u) => u.floorUnitId.toString() === unitId);
      if (unit) {
        unitsInfo.push({ ...unit });
      }
    }
  });
  return unitsInfo;
});

// 统一的tooltip信息获取函数
const getTooltip = (item) => {
  const funTypeName = fun_type.value?.find(f => f.value === `${item.funType}`);
  const tooltipInfo = [];
  tooltipInfo.push(`${funTypeName.label || "未设置"}`);

  // 只有已出租(1)或在谈客户(3)才显示企业信息
  if (item.funType === 1 || item.funType === 3) {
    tooltipInfo.push(`入驻企业：${item.enterpriseName || "无"}`);
    tooltipInfo.push(`企业代码：${item.enterpriseUscCode || "无"}`);
  }

  // 判断是合并单元还是普通单元
  if (item.group) {
    // 合并单元：显示所有子单元的名称
    const unitNames = item.children?.map(unit => unit.floorUnitName || unit.floorUnitId).join("、") || "";
    tooltipInfo.push(`单元号：【${unitNames}】`);
  } else {
    // 普通单元：显示详细信息
    tooltipInfo.push(`单元号：${item.floorUnitName || item.floorUnitId || "未设置"}`);
    tooltipInfo.push(`建筑面积：${item.buildingArea || "未设置"}㎡`);
    tooltipInfo.push(`实用面积：${item.coverArea || "未设置"}㎡`);
    tooltipInfo.push(`朝向：${item.chaoxiang || "未设置"}`);
  }

  return tooltipInfo.join("\n");
};

// 根据功能类型返回对应颜色
const formatColor = (funType) => {
  const colorMap = {
    0: '#FFFFFF', // 空置 - 白色
    1: '#FFFF00', // 已出租 - 黄色
    2: '#00FF00', // 功能区 - 绿色
    3: '#87CEEB', // 在谈客户 - 浅蓝色
    4: '#808080', // 不存在 - 灰色
    5: '#FF0000'  // 防火层 - 红色
  };

  return colorMap[funType] || '#409EFF'; // 默认颜色
};

// 获取租控单元样式
const getRentControlCellStyle = (rentControl) => {
  const unitKey = `${rentControl.floorId}-${rentControl.floorUnitId}`;
  const isSelected = selectedUnits.value.has(unitKey);
  const color = formatColor(rentControl.funType);

  return {
    backgroundColor: color, // 添加透明度
    borderColor: isSelected ? '#3b82f6' : '#d1d5dc',
    borderWidth: isSelected ? '3px' : '2px',
  };
};

// ==================== 楼宇相关操作 ====================

/** 获取楼宇列表 */
const getBuildingList = async () => {
  try {
    buildingLoading.value = true;
    const response = await listBuilding({ pageNum: 1, pageSize: 1000 });
    buildingOptions.value = response.rows || [];
  } catch (error) {
    console.error("获取楼宇列表失败:", error);
    ElMessage.error("获取楼宇列表失败");
  } finally {
    buildingLoading.value = false;
  }
};

/** 楼宇选择变化处理 */
const handleBuildingChange = (buildingId) => {
  if (buildingId) {
    loadBuildingData(buildingId);
  } else {
    floorList.value = [];
  }
};

/** 加载楼宇数据 */
const loadBuildingData = async (buildingId) => {
  loading.value = true;
  try {
    // 调用真实接口获取楼栋租控数据
    const response = await getBuildingRentControl({
      buildingId: buildingId,
    });

    if (response.data && response.data.floors) {
      // 直接使用接口返回的楼层数据
      originalFloorList.value = response.data.floors;
      console.log("接口返回的楼层数据:", originalFloorList.value);

      // 生成分组数据
      groupedFloorList.value = groupUnitsByEnterprise(response.data.floors);
      console.log("分组后的数据:", groupedFloorList.value);
    } else {
      originalFloorList.value = [];
      groupedFloorList.value = [];
    }

    // 根据当前模式设置显示数据
    updateDisplayData();
  } catch (error) {
    console.error("加载楼宇数据失败:", error);
    ElMessage.error("加载楼宇数据失败");
  } finally {
    loading.value = false;
  }
};



/** 根据当前模式更新显示数据 */
const updateDisplayData = () => {
  if (currentMode.value === 'view') {
    // 展示模式：使用分组数据
    floorList.value = groupedFloorList.value;
  } else {
    // 编辑模式：使用原始数据
    floorList.value = originalFloorList.value;
    console.log("原始楼层数据:", floorList.value);
  }
};

/** 分组函数 */
const groupUnitsByEnterprise = (floors) => {
  return floors.map(floor => {
    const groupedUnits = [];
    const rentControls = floor.rentControls || [];

    let i = 0;
    while (i < rentControls.length) {
      const currentRentControl = rentControls[i];

      // 优先判断防火层或功能区分组
      if (currentRentControl.funType === 5 || currentRentControl.funType === 2) {
        const groupChildren = [currentRentControl];
        let j = i + 1;

        // 查找连续的相同功能类型单元（防火层或功能区）
        while (j < rentControls.length &&
          rentControls[j].funType === currentRentControl.funType) {
          groupChildren.push(rentControls[j]);
          j++;
        }

        // 如果找到多个连续的相同功能类型单元，创建分组
        if (groupChildren.length > 1) {
          const groupName = currentRentControl.funType === 5 ? '防火层' : '功能区';
          groupedUnits.push({
            group: true,
            enterpriseName: groupName,
            enterpriseId: null,
            enterpriseUscCode: null,
            color: formatColor(currentRentControl.funType),
            funType: currentRentControl.funType,
            children: groupChildren
          });
        } else {
          // 单个单元直接添加
          groupedUnits.push({
            ...currentRentControl,
            color: formatColor(currentRentControl.funType)
          });
        }

        i = j; // 跳过已处理的单元
      }
      // 判断企业分组
      else if (currentRentControl.enterpriseName) {
        const groupChildren = [currentRentControl];
        let j = i + 1;

        // 查找连续的同企业单元（按数组索引顺序）
        // 需要企业ID和功能类型都一致才能合并
        while (j < rentControls.length &&
          rentControls[j].enterpriseName === currentRentControl.enterpriseName &&
          rentControls[j].funType === currentRentControl.funType) {
          groupChildren.push(rentControls[j]);
          j++;
        }

        // 如果找到多个连续的同企业单元，创建分组
        if (groupChildren.length > 1) {
          groupedUnits.push({
            group: true,
            enterpriseName: currentRentControl.enterpriseName,
            enterpriseId: currentRentControl.enterpriseId || null,
            enterpriseUscCode: currentRentControl.enterpriseUscCode || null,
            color: formatColor(currentRentControl.funType),
            funType: currentRentControl.funType,
            children: groupChildren
          });
        } else {
          // 单个单元直接添加
          groupedUnits.push({
            ...currentRentControl,
            color: formatColor(currentRentControl.funType)
          });
        }

        i = j; // 跳过已处理的单元
      }
      // 其他单元直接添加
      else {
        groupedUnits.push({
          ...currentRentControl,
          color: formatColor(currentRentControl.funType)
        });
        i++;
      }
    }

    return {
      ...floor,
      rentControls: groupedUnits
    };
  });
};

/** 刷新楼宇数据 */
const refreshBuildingData = () => {
  if (queryParams.value.selectedBuildingId) {
    loadBuildingData(queryParams.value.selectedBuildingId);
  } else {
    ElMessage.warning("请先选择楼宇");
  }
};

// ==================== 多选相关操作 ====================

/** 选择开始 */
const onSelectionStart = ({ event, selection }) => {
  // 如果不是按住Ctrl/Cmd键，清除之前的选择
  if (!event?.ctrlKey && !event?.metaKey) {
    selection.clearSelection();
    selectedUnits.value.clear();
  }
};

/** 选择移动 */
const onSelectionMove = ({
  store: {
    changed: { added, removed },
  },
}) => {
  // 添加新选中的单元格
  added.forEach((el) => {
    const unitKey = el.getAttribute("data-key");
    if (unitKey) selectedUnits.value.add(unitKey);
  });

  // 移除取消选中的单元格
  removed.forEach((el) => {
    const unitKey = el.getAttribute("data-key");
    if (unitKey) selectedUnits.value.delete(unitKey);
  });
};

/** 清除选择 */
const clearSelection = () => {
  selectedUnits.value.clear();
};

// ==================== 企业相关操作 ====================

/** 显示企业绑定弹窗 */
const showEnterpriseDialog = () => {
  if (currentMode.value !== 'edit') {
    ElMessage.warning("请切换到编辑模式");
    return;
  }

  if (selectedUnits.value.size === 0) {
    ElMessage.warning("请先选择单元格");
    return;
  }

  enterpriseDialogOpen.value = true;
  enterpriseForm.value = {
    enterpriseId: null,
    enterpriseName: "",
    unitType: null,
  };
  selectedEnterprise.value = null;
};

/** 自动完成搜索企业 */
const queryEnterpriseSearch = async (queryString, callback) => {
  if (!queryString) {
    callback([]);
    return;
  }

  try {
    const response = await listEnterprise({
      pageNum: 1,
      pageSize: 1000,
      name: queryString,
    });

    const suggestions = response.rows || [];
    callback(suggestions);
  } catch (error) {
    console.error("搜索企业失败:", error);
    callback([]);
  }
};

/** 企业选择处理 */
const handleEnterpriseSelect = (item) => {
  selectedEnterprise.value = item;
  enterpriseForm.value.enterpriseId = item.id;
  enterpriseForm.value.enterpriseName = item.name;
};

/** 企业输入框清空处理 */
const handleEnterpriseClear = () => {
  selectedEnterprise.value = null;
  enterpriseForm.value.enterpriseId = null;
};

/** 企业输入框输入处理 */
const handleEnterpriseInput = (value) => {
  if (selectedEnterprise.value && value !== selectedEnterprise.value.name) {
    selectedEnterprise.value = null;
    enterpriseForm.value.enterpriseId = null;
  }
};

/** 类型变化处理 */
const handleTypeChange = (value) => {
  console.log("类型变化:", value, "企业字段可用:", showEnterpriseFields.value);
};

/** 取消企业绑定弹窗 */
const cancelEnterpriseDialog = () => {
  enterpriseDialogOpen.value = false;
  selectedUnits.value.clear();
  selectedEnterprise.value = null;
  selectedEnterpriseId.value = null;
};

/** 提交企业绑定表单 */
const submitEnterpriseForm = async () => {
  // 表单验证
  try {
    await enterpriseFormRef.value.validate();
  } catch (error) {
    return;
  }

  // 如果类型需要企业信息，则验证企业名称
  if (showEnterpriseFields.value) {
    if (!enterpriseForm.value.enterpriseName) {
      ElMessage.warning("请输入企业名称");
      return;
    }

  }

  try {
    enterpriseSubmitting.value = true;

    const enterpriseId = selectedEnterprise.value ? selectedEnterprise.value.id : null;
    const enterpriseName = enterpriseForm.value.enterpriseName;

    const unitType = enterpriseForm.value.unitType;

    // 准备提交给后端的数据
    const submitData = {
      enterpr: {
        enterpriseId,
        enterpriseName,
      },
      unitType,
      units: selectedUnitsInfo.value.map((unitInfo) => ({
        floorId: unitInfo.floorId,
        floorUnitId: unitInfo.floorUnitId,
        buildingId: unitInfo.buildingId,
      })),
    };

    // TODO: 调用后端API保存企业绑定信息和颜色
    console.log("提交给后端的数据:", submitData);

    ElMessage.success(`成功绑定企业：${enterpriseName}`);
    cancelEnterpriseDialog();
  } catch (error) {
    console.error("企业绑定失败:", error);
    ElMessage.error("企业绑定失败");
  } finally {
    enterpriseSubmitting.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  getBuildingList();
});

watchEffect(() => {
  if (selectionAreaRef.value?.selection) {
    console.log('SelectionArea已初始化:', selectionAreaRef.value.selection);

    // 根据当前模式设置初始状态
    if (currentMode.value === 'edit') {
      selectionAreaRef.value.selection.enable();
    } else {
      selectedUnits.value.clear();
      selectionAreaRef.value.selection.disable();
    }
    updateDisplayData();
  }
});
</script>

<style>
/* SelectionArea 容器样式 */
.selection-container {
  user-select: none;
}

/* Viselect 选择区域样式 */
.selection-area {
  background: rgba(46, 115, 252, 0.11);
  border: 1px solid rgba(98, 155, 255, 0.85);
  border-radius: 0.15em;
}

/* 自定义滚动条样式 */
.scrollbar-custom {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.scrollbar-custom::-webkit-scrollbar {
  width: 8px;
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.scrollbar-custom::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 选中状态样式 */
.unit-cell.selected {
  border-color: #3b82f6 !important;
  border-width: 3px !important;
}

/* 企业信息显示区域样式 - 副信息样式 */
.enterprise-info-display {
  margin: 12px 0 0 0;
  /* 不需要左边距，因为已经在form-item内部 */
  width: 400px;
  /* 与输入框宽度一致 */
}

.enterprise-details {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  font-size: 14px;
}

.info-title {
  font-weight: 600;
  color: #64748b;
  margin-bottom: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 6px;
}

.info-item {
  font-size: 14px;
  line-height: 1.4;
  color: #64748b;
}

.info-label {
  color: #475569;
  font-weight: 500;
}

.info-value {
  color: #1e293b;
  margin-left: 4px;
  font-weight: 400;
}

.no-enterprise {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 12px;
  text-align: center;
}

.no-enterprise .el-empty {
  padding: 8px 0;
}

.no-enterprise .el-empty__description {
  font-size: 12px;
  color: #dc2626;
  margin-top: 4px;
}
</style>
