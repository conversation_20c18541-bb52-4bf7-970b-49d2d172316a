<template>
  <!-- 基本信息 Tab -->
  <div class="tab-pane">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在获取楼宇详细信息...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <el-icon class="error-icon"><Warning /></el-icon>
      <p>{{ error }}</p>
      <button @click="fetchBuildingDetail" class="retry-btn">重试</button>
    </div>

    <!-- 正常内容 -->
    <div v-else class="info-layout">
      <!-- 楼宇全景图片区域 -->
      <div class="building-image-section">
        <div class="panorama-container">
          <PanoramaViewer :panorama-url="panoramaImageUrl" />
        </div>
        <div class="building-name">{{ currentBuildingData.name || '暂无数据' }}</div>
      </div>

      <!-- 楼宇信息区域 -->
      <div class="building-info-section">
        <div class="info-cards">
          <!-- 基本信息卡片 -->
          <div class="info-card">
            <div class="card-header">
              <el-icon class="card-icon"><OfficeBuilding /></el-icon>
              <span class="card-title">基本信息</span>
            </div>
            <div class="card-content">
              <div class="info-item">
                <span class="label">楼宇名称</span>
                <span class="value">{{ currentBuildingData.name || '暂无数据' }}</span>
              </div>
              <div class="info-item">
                <span class="label">楼层数</span>
                <span class="value">{{ currentBuildingData.floorNum || '暂无数据' }}层</span>
              </div>
              <div class="info-item">
                <span class="label">总建筑面积</span>
                <span class="value">{{ formatNumber(currentBuildingData.buildingArea) }}㎡</span>
              </div>
              <div class="info-item">
                <span class="label">总占地面积</span>
                <span class="value">{{ formatNumber(currentBuildingData.coverArea) }}㎡</span>
              </div>
            </div>
          </div>

          <!-- 面积信息卡片 -->
          <div class="info-card">
            <div class="card-header">
              <el-icon class="card-icon"><Crop /></el-icon>
              <span class="card-title">面积信息</span>
            </div>
            <div class="card-content">
              <div class="info-item">
                <span class="label">总商业面积</span>
                <span class="value">{{ formatNumber(currentBuildingData.businessArea) }}㎡</span>
              </div>
              <div class="info-item">
                <span class="label">总办公面积</span>
                <span class="value">{{ formatNumber(currentBuildingData.officialArea) }}㎡</span>
              </div>
              <div class="info-item">
                <span class="label">商业可招商面积</span>
                <span class="value highlight">{{ formatNumber(currentBuildingData.isBusinessArea) }}㎡</span>
              </div>
              <div class="info-item">
                <span class="label">办公可招商面积</span>
                <span class="value highlight">{{ formatNumber(currentBuildingData.isOfficialArea) }}㎡</span>
              </div>
            </div>
          </div>

          <div class="info-card">
            <div class="card-header">
              <el-icon class="card-icon"><TrendCharts /></el-icon>
              <span class="card-title">运营信息</span>
            </div>
            <div class="card-content">
              <div class="info-item">
                <span class="label">主导行业</span>
                <span class="value">{{ currentBuildingData.leadingIndustry || '暂无数据' }}</span>
              </div>
              <div class="info-item">
                <span class="label">进驻企业数量</span>
                <span class="value highlight">{{ currentBuildingData.companiesNum || '0' }}家</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, ref, onMounted, computed } from 'vue';
import { getBuilding } from '@/api/building/building';
import PanoramaViewer from '../PanoramaViewer.vue';
import { OfficeBuilding, Crop, TrendCharts, Warning } from "@element-plus/icons-vue";

const props = defineProps({
  buildingData: {
    type: Object,
    default: () => ({})
  }
});

// 响应式数据
const loading = ref(false);
const buildingDetail = ref({});
const error = ref('');

// 计算属性：优先使用API获取的详细数据，否则使用传入的基础数据
const currentBuildingData = computed(() => {
  return Object.keys(buildingDetail.value).length > 0 ? buildingDetail.value : props.buildingData;
});

// 全景图片URL - 优先使用楼宇数据中的全景图片，否则使用默认图片
const panoramaImageUrl = computed(() => {
  // 如果楼宇数据中有全景图片URL，使用它
  if (currentBuildingData.value.panoramaImage) {
    return currentBuildingData.value.panoramaImage;
  }
  // 否则使用默认的全景图片
  return new URL('/src/assets/cockpit/building.jpg', import.meta.url).href;
});



// 获取楼宇详细信息
const fetchBuildingDetail = async () => {
  if (!props.buildingData?.id) {
    console.warn('BasicInfoTab: 缺少楼宇ID，无法获取详细信息');
    return;
  }

  try {
    loading.value = true;
    error.value = '';

    console.log('BasicInfoTab: 开始获取楼宇详细信息，ID:', props.buildingData.id);
    const response = await getBuilding(props.buildingData.id);

    if (response.code === 200 && response.data) {
      buildingDetail.value = response.data;
      console.log('BasicInfoTab: 获取楼宇详细信息成功:', buildingDetail.value);
    } else {
      error.value = response.msg || '获取楼宇详细信息失败';
      console.error('BasicInfoTab: 获取楼宇详细信息失败:', response.msg);
    }
  } catch (err) {
    error.value = '网络请求异常，请稍后重试';
    console.error('BasicInfoTab: 获取楼宇详细信息异常:', err);
  } finally {
    loading.value = false;
  }
};

// 格式化数字
const formatNumber = (num) => {
  if (!num || num === '暂无数据') return '暂无数据';
  return Number(num).toLocaleString();
};

// 格式化经纬度
const formatCoordinates = () => {
  const data = currentBuildingData.value;
  const longitude = data.longitude;
  const latitude = data.latitude;

  if (!longitude || !latitude) return '暂无数据';

  return `${parseFloat(longitude).toFixed(6)}, ${parseFloat(latitude).toFixed(6)}`;
};

// 组件挂载时获取详细信息
onMounted(() => {
  fetchBuildingDetail();
});
</script>

<style scoped>
/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #00feff;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 254, 255, 0.3);
  border-top: 3px solid #00feff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #ff6b6b;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.retry-btn {
  margin-top: 15px;
  padding: 8px 20px;
  background: linear-gradient(135deg, #00feff 0%, #0095ff 100%);
  border: none;
  border-radius: 4px;
  color: #fff;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: linear-gradient(135deg, #0095ff 0%, #00feff 100%);
  transform: translateY(-1px);
}

/* 基本信息Tab布局 */
.info-layout {
  display: flex;
  gap: 20px;
  height: 100%;
}

.building-image-section {
  flex: 0 0 450px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.panorama-container {
  width: 100%;
  height: 320px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 149, 255, 0.3);
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.8) 0%,
    rgba(25, 55, 125, 0.9) 100%);
  position: relative;
}

.panorama-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    transparent 30%,
    rgba(0, 254, 255, 0.1) 50%,
    transparent 70%);
  pointer-events: none;
  z-index: 1;
}

.building-name {
  font-size: 20px;
  font-weight: 600;
  color: #00feff;
  text-align: center;
  padding: 12px;
  background: rgba(20, 50, 120, 0.6);
  border: 1px solid rgba(0, 149, 255, 0.3);
  border-radius: 8px;
  text-shadow: 0 0 8px rgba(0, 254, 255, 0.3);
}

/* 楼宇信息区域 */
.building-info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-cards {
  display: flex;
  flex-direction: column;
  gap: 15px;
  height: 100%;
  overflow-y: auto;
}

.info-card {
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.4) 0%,
    rgba(25, 55, 125, 0.6) 100%);
  border: 1px solid rgba(0, 149, 255, 0.2);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #00feff, #027eff);
}

.info-card:hover {
  border-color: rgba(0, 149, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 149, 255, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 149, 255, 0.2);
}

.card-icon {
  font-size: 16px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #00feff;
  flex: 1;
}

.card-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 15px;
}

.value {
  color: #00feff;
  font-weight: 500;
  font-size: 15px;
}

.value.highlight {
  color: #00ff88;
  font-weight: 600;
  text-shadow: 0 0 5px rgba(0, 255, 136, 0.3);
}

/* 滚动条样式 */
.info-cards::-webkit-scrollbar {
  width: 6px;
}

.info-cards::-webkit-scrollbar-track {
  background: rgba(20, 50, 120, 0.3);
  border-radius: 3px;
}

.info-cards::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #00feff, rgba(0, 149, 255, 0.8));
  border-radius: 3px;
  box-shadow: inset 0 0 3px rgba(0, 254, 255, 0.3);
}

.info-cards::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #00feff, #0095ff);
}
</style>
