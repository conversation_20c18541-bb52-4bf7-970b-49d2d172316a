import request from '@/utils/request'
// 查询小区组别列表
export function listCommunityGroup(query) {
  return request({
    url: '/community/community_group/list',
    method: 'get',
    params: query
  })
}

// 查询小区组别详细
export function getCommunityGroup(id) {
  return request({
    url: '/community/community_group/' + id,
    method: 'get'
  })
}

// 新增小区组别
export function addCommunityGroup(data) {
  return request({
    url: '/community/community_group/add',
    method: 'post',
    data: data
  })
}

// 修改小区组别
export function updateCommunityGroup(data) {
  return request({
    url: '/community/community_group/edit',
    method: 'post',
    data: data
  })
}

// 删除小区组别
export function delCommunityGroup(id) {
  return request({
    url: '/community/community_group/' + id,
    method: 'delete'
  })
}

