import request from '@/utils/request'
// 查询单元管理列表
export function listFloorUnit(query) {
  return request({
    url: '/building/floor_unit/list',
    method: 'get',
    params: query
  })
}

// 查询单元管理详细
export function getFloorUnit(id) {
  return request({
    url: '/building/floor_unit/' + id,
    method: 'get'
  })
}

// 新增单元管理
export function addFloorUnit(data) {
  return request({
    url: '/building/floor_unit/add',
    method: 'post',
    data: data
  })
}

// 修改单元管理
export function updateFloorUnit(data) {
  return request({
    url: '/building/floor_unit/edit',
    method: 'post',
    data: data
  })
}

// 删除单元管理
export function delFloorUnit(id) {
  return request({
    url: '/building/floor_unit/' + id,
    method: 'delete'
  })
}

// 复制单元管理
export function copyFloorUnit(floorUnitId, num) {
  return request({
    url: '/building/floor_unit/copyFloorUnit',
    method: 'get',
    params: {
      floorUnitId: floorUnitId,
      num: num
    }
  })
}

