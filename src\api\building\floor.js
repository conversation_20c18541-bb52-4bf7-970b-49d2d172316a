import request from '@/utils/request'
// 查询楼层信息列表
export function listFloor(query) {
  return request({
    url: '/building/floor/list',
    method: 'get',
    params: query
  })
}

// 查询楼层信息详细
export function getFloor(id) {
  return request({
    url: '/building/floor/' + id,
    method: 'get'
  })
}

// 新增楼层信息
export function addFloor(data) {
  return request({
    url: '/building/floor/add',
    method: 'post',
    data: data
  })
}

// 修改楼层信息
export function updateFloor(data) {
  return request({
    url: '/building/floor/edit',
    method: 'post',
    data: data
  })
}

// 删除楼层信息
export function delFloor(id) {
  return request({
    url: '/building/floor/' + id,
    method: 'delete'
  })
}

// 复制楼层信息
export function copyFloor(floorId, num) {
  return request({
    url: '/building/floor/copyFloor',
    method: 'get',
    params: {
      floorId: floorId,
      num: num
    }
  })
}

