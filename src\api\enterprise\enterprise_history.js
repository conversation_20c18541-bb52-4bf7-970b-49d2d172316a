import request from '@/utils/request'
// 查询企业信息历史备份列表
export function listEnterpriseHistory(query) {
  return request({
    url: '/enterprise/enterprise_history/list',
    method: 'get',
    params: query
  })
}

// 查询企业信息历史备份详细
export function getEnterpriseHistory(id) {
  return request({
    url: '/enterprise/enterprise_history/' + id,
    method: 'get'
  })
}

// 新增企业信息历史备份
export function addEnterpriseHistory(data) {
  return request({
    url: '/enterprise/enterprise_history/add',
    method: 'post',
    data: data
  })
}

// 修改企业信息历史备份
export function updateEnterpriseHistory(data) {
  return request({
    url: '/enterprise/enterprise_history/edit',
    method: 'post',
    data: data
  })
}

// 删除企业信息历史备份
export function delEnterpriseHistory(id) {
  return request({
    url: '/enterprise/enterprise_history/' + id,
    method: 'delete'
  })
}

