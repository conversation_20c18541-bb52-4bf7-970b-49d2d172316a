<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button icon="ArrowLeft" @click="goBack">返回楼宇列表</el-button>
        <span class="building-info">{{ buildingName }} - 楼层管理</span>
      </div>
    </div>

    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="楼宇选择" prop="buildingId">
        <el-select v-model="queryParams.buildingId" placeholder="请选择楼宇" clearable style="width: 200px" @change="handleBuildingChange">
          <el-option
            v-for="building in buildingList"
            :key="building.id"
            :label="building.name"
            :value="building.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['building:floor:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate" v-hasPermi="['building:floor:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="CopyDocument" :disabled="single" @click="handleCopy" v-hasPermi="['building:floor:copy']"
          >复制</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['building:floor:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport" v-hasPermi="['building:floor:import']">导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['building:floor:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="floorList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
       <el-table-column label="楼层编号" align="center" prop="id" width="150" />
      <el-table-column label="楼宇名称" align="center" prop="buildingNameFormatted" width="150" />
      <el-table-column label="楼层名称" align="center" prop="floorNameFormatted" width="150" />
      <el-table-column label="总占地面积" align="center" prop="coverAreaFormatted" width="130" />
      <el-table-column label="总建筑面积" align="center" prop="buildingAreaFormatted" width="130" />
      <el-table-column label="单元数量" align="center" prop="unitNum" width="120">
        <template #default="scope">
          <span>{{ scope.row.unitNum || 0 }}个</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="260" fixed="right">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['building:floor:edit']">修改</el-button>
          <el-button link type="success" icon="House" @click="handleUnits(scope.row)" v-hasPermi="['building:unit:list']">单元</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['building:floor:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改楼层信息对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="floorRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="楼层号" prop="name">
          <el-input-number
            v-model="form.name"
            :min="1"
            :max="100"
            :precision="0"
            placeholder="请输入楼层号"
            style="width: 200px"
          />
          <span style="margin-left: 8px; color: #909399;">F</span>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport + (queryParams.buildingId ? '&buildingId=' + queryParams.buildingId : '')"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip"><el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据</div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 复制楼层对话框 -->
    <el-dialog title="复制楼层" v-model="copyDialog.open" width="400px" append-to-body>
      <el-form ref="copyFormRef" :model="copyDialog.form" :rules="copyDialog.rules" label-width="100px">
        <el-form-item label="楼层信息">
          <span>{{ copyDialog.floorInfo }}</span>
        </el-form-item>
        <el-form-item label="复制数量" prop="num">
          <el-input-number
            v-model="copyDialog.form.num"
            :min="1"
            :max="50"
            placeholder="请输入复制数量"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="copyDialog.open = false">取 消</el-button>
          <el-button type="primary" @click="submitCopyForm" :loading="copyDialog.loading">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="floor">
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getToken } from "@/utils/auth";
import { listFloor, getFloor, delFloor, addFloor, updateFloor, copyFloor } from "@/api/building/floor";
import { getBuilding, listBuilding } from "@/api/building/building";
const { proxy } = getCurrentInstance();
const route = useRoute()
const router = useRouter()
const floorList = ref([]);
const buildingList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const buildingId = ref(null);
const buildingName = ref('');
const copyFormRef = ref();

// 复制对话框数据
const copyDialog = reactive({
  open: false,
  loading: false,
  floorId: null,
  floorInfo: '',
  form: {
    num: 1
  },
  rules: {
    num: [
      { required: true, message: '复制数量不能为空', trigger: 'blur' },
      { type: 'number', min: 1, max: 50, message: '复制数量必须在1-50之间', trigger: 'blur' }
    ]
  }
});
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    buildingId: null,
  },
  rules: ref({
    name: [
      { required: true, message: '楼层号不能为空', trigger: 'blur' },
      { type: 'number', min: 1, max: 100, message: '楼层号必须在1-100之间', trigger: 'blur' }
    ]
  }),
});
const { queryParams, form, rules } = toRefs(data);

/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层
  open: false,
  // 弹出层标题（导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/building/floor/importData",
});

/** 格式化面积显示 */
const formatArea = (area) => {
  if (!area) return '-'
  return `${area}㎡`
}

/** 根据楼宇ID获取楼宇名称 */
const getBuildingName = (buildingId) => {
  if (!buildingId || !buildingList.value.length) return '-'
  const building = buildingList.value.find(item => item.id == buildingId)
  return building ? building.name : '-'
}

/** 格式化楼层名称显示 */
const formatFloorName = (name) => {
  if (!name) return '-'
  return `${name}F`
}

/** 获取楼宇列表 */
const getBuildingList = async () => {
  try {
    const response = await listBuilding({ pageSize: 1000 })
    buildingList.value = response.rows || []
  } catch (error) {
    console.error('获取楼宇列表失败:', error)
    proxy.$modal.msgError('获取楼宇列表失败')
  }
}

/** 获取楼宇信息 */
const getBuildingInfo = async (id) => {
  try {
    const response = await getBuilding(id)
    buildingName.value = response.data?.name || '未知楼宇'
  } catch (error) {
    console.error('获取楼宇信息失败:', error)
    buildingName.value = '未知楼宇'
  }
}

/** 楼宇选择变化处理 */
const handleBuildingChange = (selectedBuildingId) => {
  queryParams.value.buildingId = selectedBuildingId
  if (selectedBuildingId) {
    getBuildingInfo(selectedBuildingId)
  }
  handleQuery()
}

/** 返回楼宇列表 */
const goBack = () => {
  router.push('/building/building')
}

/** 单元按钮操作 */
const handleUnits = (row) => {
  router.push(`/building/floor_unit/${buildingId.value}/${row.id}`)
}

/** 复制按钮操作 */
const handleCopy = () => {
  const floorId = ids.value[0]
  const selectedFloor = floorList.value.find(item => item.id === floorId)

  if (!selectedFloor) {
    proxy.$modal.msgError('请选择要复制的楼层')
    return
  }

  // 确保复制的楼层属于当前筛选选中的楼宇
  if (queryParams.value.buildingId && selectedFloor.buildingId !== queryParams.value.buildingId) {
    proxy.$modal.msgError('只能复制当前筛选楼宇下的楼层')
    return
  }

  copyDialog.floorId = selectedFloor.id
  copyDialog.floorInfo = `${selectedFloor.floorNameFormatted} (${selectedFloor.buildingNameFormatted})`
  copyDialog.form.num = 1
  copyDialog.open = true
}

/** 提交复制表单 */
const submitCopyForm = async () => {
  if (!copyFormRef.value) return

  try {
    await copyFormRef.value.validate()
    copyDialog.loading = true

    await copyFloor(copyDialog.floorId, copyDialog.form.num)

    proxy.$modal.msgSuccess(`成功复制 ${copyDialog.form.num} 个楼层`)
    copyDialog.open = false
    getList()
  } catch (error) {
    console.error('复制楼层失败:', error)
    proxy.$modal.msgError('复制楼层失败，请重试')
  } finally {
    copyDialog.loading = false
  }
}

/** 查询楼层信息列表 */
function getList() {
  loading.value = true;
  listFloor(queryParams.value).then((response) => {
    // 预处理数据，添加格式化字段
    floorList.value = (response.rows || []).map(item => ({
      ...item,
      buildingNameFormatted: getBuildingName(item.buildingId),
      floorNameFormatted: formatFloorName(item.name),
      coverAreaFormatted: formatArea(item.coverArea),
      buildingAreaFormatted: formatArea(item.buildingArea)
    }))
    total.value = response.total;
    loading.value = false;
  });
}
// 取消按钮
function cancel() {
  open.value = false;
  reset();
}
// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    remark: null,
  };
  proxy.resetForm("floorRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  // 重置时保持当前buildingId
  queryParams.value.buildingId = buildingId.value;
  handleQuery();
}
// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
  reset();
  // 自动填充当前筛选选中的楼宇ID
  if (queryParams.value.buildingId) {
    form.value.buildingId = queryParams.value.buildingId;
  }
  open.value = true;
  title.value = "添加楼层信息";
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getFloor(_id).then((response) => {
    form.value = response.data;
    form.value.name = Number(response.data.name)
    open.value = true;
    title.value = "修改楼层信息";
  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["floorRef"].validate((valid) => {
    if (valid) {
      // 确保包含当前筛选选中的楼宇ID
      const formData = {
        ...form.value,
        buildingId: queryParams.value.buildingId || form.value.buildingId
      };

      if (form.value.id != null) {
        updateFloor(formData).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addFloor(formData).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm("是否确认删除楼层编号为" + _ids + "的数据项？")
    .then(function () {
      return delFloor(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "building/floor/export",
    {
      ...queryParams.value, // 直接使用当前筛选条件，包含用户选择的楼宇ID
    },
    `楼层信息_${new Date().getTime()}.xlsx`
  );
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "楼层信息导入";
  upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download("building/floor/downTemplate", {}, `楼层信息_${new Date().getTime()}.xlsx`);
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}
// 页面初始化
onMounted(async () => {
  // 1. 从URL路由参数中获取buildingId
  buildingId.value = Number(route.params.buildingId)
  queryParams.value.buildingId = buildingId.value

  // 2. 先获取所有楼宇列表，确保楼宇名称能正确显示
  await getBuildingList()

  // 3. 获取当前楼宇信息用于页面标题显示
  if (buildingId.value) {
    await getBuildingInfo(buildingId.value)
  }

  // 4. 获取指定楼宇下的楼层列表数据
  getList()
})
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
  padding: 16px 0;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.building-info {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}
</style>
