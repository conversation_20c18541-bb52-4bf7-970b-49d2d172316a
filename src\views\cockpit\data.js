// 地图数据
export const datas_ditu = [
  {name: '锅炉', attr1: 117.236441, attr2: 31.816848, attr3: '合肥市新华大道120号', attr4: '超期未检', attr5: 'GL32012078J', attr6: '锅炉78j', attr7: '安徽特检'},
  {name: '锅炉', attr1: 117.397073, attr2: 31.738776, attr3: '合肥市新华大道121号', attr4: '合格', attr5: 'GL32012079J', attr6: '锅炉79j', attr7: '淮南特检'},
  {name: '锅炉', attr1: 117.990678, attr2: 31.987791, attr3: '合肥市新华大道122号', attr4: '不合格', attr5: 'GL32012080J', attr6: '锅炉80j', attr7: '芜湖特检'},
  {name: '锅炉', attr1: 117.249445, attr2: 31.782563, attr3: '合肥市新华大道123号', attr4: '合格', attr5: 'GL32012081J', attr6: '锅炉81j', attr7: '徐州特检'},
  {name: '锅炉', attr1: 117.705034, attr2: 32.891792, attr3: '合肥市新华大道124号', attr4: '超期未检', attr5: 'GL32012082J', attr6: '锅炉82j', attr7: '宣城特检'},
  {name: '电梯', attr1: 117.047571, attr2: 31.93507, attr3: '合肥市新华大道130号', attr4: '超期未检', attr5: 'DT32012078J', attr6: '电梯78j', attr7: '安徽特检'},
  {name: '电梯', attr1: 117.192367, attr2: 31.828079, attr3: '合肥市新华大道131号', attr4: '合格', attr5: 'DT32012079J', attr6: '电梯79j', attr7: '淮南特检'},
  {name: '电梯', attr1: 117.217215, attr2: 31.810138, attr3: '合肥市新华大道132号', attr4: '不合格', attr5: 'DT32012080J', attr6: '电梯80j', attr7: '芜湖特检'},
  {name: '压力管道', attr1: 117.29133, attr2: 31.950511, attr3: '合肥市新华大道144号', attr4: '超期未检', attr5: 'YLGD32012092J', attr6: '压力管道92j', attr7: '宣城特检'},
  {name: '压力管道', attr1: 117.376474, attr2: 31.779352, attr3: '合肥市新华大道145号', attr4: '超期未检', attr5: 'YLGD32012093J', attr6: '压力管道93j', attr7: '铜陵特检'},
  {name: '客运索道', attr1: 117.147821, attr2: 31.781687, attr3: '合肥市新华大道152号', attr4: '合格', attr5: 'KYSD32012086J', attr6: '客运索道86J', attr7: '芜湖特检'},
  {name: '客运索道', attr1: 117.056841, attr2: 31.787816, attr3: '合肥市新华大道153号', attr4: '超期未检', attr5: 'KYSD32012087J', attr6: '客运索道87J', attr7: '徐州特检'},
  {name: '场内车辆', attr1: 117.353471, attr2: 31.939441, attr3: '合肥市新华大道157号', attr4: '超期未检', attr5: 'CNCL32012080J', attr6: '场内车辆80J', attr7: '安庆特检'},
  {name: '场内车辆', attr1: 117.457842, attr2: 32.471053, attr3: '合肥市新华大道158号', attr4: '合格', attr5: 'CNCL32012081J', attr6: '场内车辆81J', attr7: '淮北特检'},
  {name: '游乐设施', attr1: 117.241291, attr2: 31.81466, attr3: '合肥市新华大道163号', attr4: '不合格', attr5: 'YLSS32012080J', attr6: '游乐设施80J', attr7: '徐州特检'},
  {name: '游乐设施', attr1: 117.07332, attr2: 30.27162, attr3: '合肥市新华大道164号', attr4: '超期未检', attr5: 'YLSS32012081J', attr6: '游乐设施81J', attr7: '宣城特检'},
  {name: '起重机械', attr1: 117.271074, attr2: 34.196564, attr3: '合肥市新华大道167号', attr4: '合格', attr5: 'QZJX32012080J', attr6: '起重机械80J', attr7: '安庆特检'},
  {name: '起重机械', attr1: 116.304277, attr2: 31.107722, attr3: '合肥市新华大道168号', attr4: '超期未检', attr5: 'QZJX32012081J', attr6: '起重机械81J', attr7: '淮北特检'},
  {name: '压力容器', attr1: 117.248715, attr2: 31.814003, attr3: '合肥市新华大道175号', attr4: '合格', attr5: 'QZJX32012088J', attr6: '起重机械88J', attr7: '铜陵特检'},
  {name: '压力容器', attr1: 117.0898, attr2: 31.267498, attr3: '合肥市新华大道176号', attr4: '不合格', attr5: 'YLRQ32012080J', attr6: '压力容器80J', attr7: '阜阳特检'}
];

// 业务数量
export const datas2 = [
  {name: '今日申报数量', value: 120},
  {name: '今日受理数量', value: 120},
  {name: '今日检验数量', value: 100},
  {name: '今日不合格数量', value: 20}
];

// 检验单位
export const datas3 = [
  {name: '安徽特检', value: 1200000},
  {name: '淮南特检', value: 1560000},
  {name: '芜湖特检', value: 2000000},
  {name: '徐州特检', value: 3610000},
  {name: '宣城特检', value: 4000000},
  {name: '铜陵特检', value: 6000000},
  {name: '阜阳特检', value: 5300000},
  {name: '安庆特检', value: 8000000},
  {name: '淮北特检', value: 4800000},
  {name: '马鞍山特检', value: 7200000}
];

// 年度数量
export const datas4 = [
  {name: 2014, value: '超期未检', attr2: 5200000},
  {name: 2014, value: '注册数量', attr2: 11200000},
  {name: 2015, value: '超期未检', attr2: 3000000},
  {name: 2015, value: '注册数量', attr2: 16000000},
  {name: 2016, value: '超期未检', attr2: 9600000},
  {name: 2016, value: '注册数量', attr2: 12300000},
  {name: 2017, value: '超期未检', attr2: 13600000},
  {name: 2017, value: '注册数量', attr2: 23650000},
  {name: 2018, value: '超期未检', attr2: 15200000},
  {name: 2018, value: '注册数量', attr2: 29000000},
  {name: 2019, value: '超期未检', attr2: 11200000},
  {name: 2019, value: '注册数量', attr2: 19560000}
];

// 设备类别
export const datas5 = [
  {name: '锅炉', value: 4200000},
  {name: '电梯', value: 2600000},
  {name: '压力管道', value: 5600000},
  {name: '客运索道', value: 1520000},
  {name: '场内车辆', value: 2300000},
  {name: '游乐设施', value: 1550000},
  {name: '起重机械', value: 3690000},
  {name: '压力容器', value: 5680000}
];

// 年龄分布
export const datas6 = [
  {name: '25以下', value: 25},
  {name: '25-45岁', value: 45},
  {name: '45岁以上', value: 30}
];

// 持证占比
export const datas7 = [
  {name: '持证', value: '54%'},
  {name: '未持证', value: '46%'}
];

// 检验报告数量
export const excelJson_24 = [
  {a: '宣城市特检', b: 14078},
  {a: '滁州市特检', b: 14514},
  {a: '芜湖市特检', b: 14565},
  {a: '淮南市特检', b: 15082},
  {a: '安徽省特检', b: 59056}
];

// 检验单位检验结论统计
export const inspectionData = {
  units: ["安徽省特检院","淮南市特检中心","芜湖市特检中心","滁州市特检中心","宣城市特检中心","铜陵市特检中心","阜阳市特检中心","安庆市特检中心","马鞍山市特检中心","淮北市特检中心","蚌埠市特检中心","黄山市特检中心","六安市特检中心","池州市特检中心","宿州市特检中心","安徽省特检院巢湖分院","安徽省特检院亳州检测部"],
  level1: [0, 0, 90, 671, 0, 0, 0, 1307, 1328, 0, 0, 458, 0, 0, 0, 0, 174],
  level2: [2665, 3785, 2335, 1800, 923, 427, 938, 571, 1636, 753, 1538, 1447, 0, 320, 0, 808, 271],
  level3: [2577, 0, 1, 638, 725, 2, 1, 1, 0, 1, 2, 692, 1, 413, 578, 710, 651],
  level4: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 0, 0, 0, 0, 0, 0],
  qualified: [46069, 6691, 5969, 2580, 6248, 5806, 1815, 3537, 3123, 5279, 4235, 4206, 4540, 2494, 2458, 2822, 846],
  supervised: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 186, 0, 332, 0, 0],
  other: [6188, 3180, 4644, 8781, 5586, 3961, 6700, 4793, 2192, 1409, 1951, 1083, 1482, 3537, 990, 944, 926],
  allowed: [1189, 219, 0, 0, 347, 443, 576, 0, 1407, 878, 0, 0, 263, 354, 934, 45, 0]
}; 