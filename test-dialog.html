<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技风弹窗测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #001d58 0%, #002554 100%);
            font-family: 'Microsoft YaHei', sans-serif;
            color: white;
            min-height: 100vh;
        }
        
        .test-container {
            text-align: center;
            padding: 50px;
        }
        
        .test-btn {
            background: linear-gradient(135deg, rgba(0, 149, 255, 0.8) 0%, rgba(0, 254, 255, 0.6) 100%);
            border: 1px solid rgba(0, 149, 255, 0.5);
            color: white;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 0 20px rgba(0, 149, 255, 0.3);
        }
        
        .test-btn:hover {
            background: linear-gradient(135deg, rgba(0, 149, 255, 1) 0%, rgba(0, 254, 255, 0.8) 100%);
            box-shadow: 0 0 30px rgba(0, 149, 255, 0.5);
            transform: translateY(-2px);
        }
        
        h1 {
            color: #00feff;
            text-shadow: 0 0 10px rgba(0, 254, 255, 0.5);
            margin-bottom: 30px;
        }
        
        .description {
            margin: 20px 0;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }
        
        .features {
            text-align: left;
            max-width: 600px;
            margin: 30px auto;
            background: rgba(0, 29, 88, 0.5);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid rgba(0, 149, 255, 0.3);
        }
        
        .features h3 {
            color: #00feff;
            margin-bottom: 15px;
        }
        
        .features ul {
            list-style: none;
            padding: 0;
        }
        
        .features li {
            padding: 5px 0;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .features li::before {
            content: "✨ ";
            color: #00feff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🏢 智慧大屏详情弹窗系统</h1>

        <div class="description">
            <p>全新设计的科技风格弹窗系统，支持商业模式和居住模式两种不同的详情展示</p>
        </div>
        
        <div class="features">
            <h3>🚀 完善的双模式弹窗系统</h3>
            <ul>
                <li>🏢 <strong>商业模式</strong> - 楼宇详情弹窗（5个Tab）</li>
                <li>🏘️ <strong>居住模式</strong> - 小区详情弹窗（2个Tab）</li>
                <li>📊 <strong>Tab1: 基本信息</strong> - 组别、名称、栋数、物管数据、入住率</li>
                <li>📈 <strong>Tab2: 统计信息</strong> - 户数统计卡片 + 人口统计卡片</li>
                <li>🎨 <strong>统一设计风格</strong> - 统计信息Tab采用与基本信息相同的卡片布局</li>
                <li>🔧 <strong>代码优化</strong> - 使用公共addBuildingMarkers方法</li>
                <li>📍 <strong>标记简化</strong> - 只显示标题，去除冗余信息</li>
                <li>⚡ <strong>参数控制</strong> - 通过传参控制不同模式的弹窗</li>
                <li>🖱️ <strong>交互优化</strong> - 文本标记也支持点击事件，避免遮挡</li>
                <li>🎯 <strong>点击体验</strong> - 标记图标和文本都可以点击打开弹窗</li>
                <li>✅ 全部表格增加分页功能</li>
                <li>✅ 每页显示5条数据</li>
                <li>✅ 每次打开弹窗回到基本信息Tab</li>
                <li>✅ 租户信息表格显示</li>
                <li>✅ 楼宇诉求管理（7条数据）</li>
                <li>✅ 招商线索管理（6条数据）</li>
                <li>✅ 企业问题反馈（7条数据）</li>
                <li>✅ 小区统计信息简洁卡片式展示</li>
                <li>✅ 科技风分页组件设计</li>
                <li>✅ 状态标签和类型标签</li>
                <li>✅ 响应式设计支持</li>
            </ul>
        </div>
        
        <button class="test-btn" onclick="openCockpit()">
            🚀 打开大屏查看效果
        </button>
        
        <div class="description" style="margin-top: 30px;">
            <p>点击按钮将打开您的智慧大屏项目，然后根据不同模式点击地图上的标记查看弹窗效果</p>
            <p style="margin-top: 10px; font-size: 14px; color: #00feff;">
                💡 测试提示：
                <br>• <strong>商业模式</strong>：点击蓝色楼宇标记查看5个Tab的详情弹窗
                <br>• <strong>居住模式</strong>：点击红色小区标记查看2个Tab的小区详情弹窗
                <br>• <strong>小区Tab1</strong>：基本信息（组别、名称、栋数、物管数据、入住率）
                <br>• <strong>小区Tab2</strong>：统计信息（户数统计卡片 + 人口统计卡片，与基本信息相同风格）
                <br>• <strong>设计统一</strong>：统计信息Tab采用与基本信息Tab相同的卡片布局风格
                <br>• <strong>标记优化</strong>：现在标记只显示标题，界面更简洁
                <br>• <strong>交互优化</strong>：标记图标和文本标签都可以点击，避免遮挡问题
                <br>• <strong>代码优化</strong>：使用统一的标记方法，通过参数控制不同模式
                <br>• 切换不同Tab查看效果，关闭弹窗再打开会自动回到基本信息Tab
            </p>
        </div>
    </div>

    <script>
        function openCockpit() {
            // 在新窗口中打开大屏项目
            window.open('http://localhost:5173/cockpit', '_blank');
        }
    </script>
</body>
</html>
