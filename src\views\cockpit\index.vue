<template>
  <div id="cockpit" class="dashboard-container w-full h-full relative overflow-hidden bg-[#001d58]">
    <!-- 页面头部 -->
    <div
      class="header w-full h-[108px] bg-no-repeat bg-cover bg-[url(@/assets/cockpit/bg/top-boder.png)] relative flex justify-between items-center px-[50px] z-10"
    >
      <!-- 时间显示 -->
      <div
        class="w-[341px] h-[65px] mb-[20px] bg-no-repeat bg-contain flex items-center justify-center bg-[url(@/assets/cockpit/bg/datatime.png)]"
      >
        <div id="time" class="text-white"></div>
      </div>

      <!-- 标题 -->
      <div
        class="w-[944px] h-[69px] bg-no-repeat bg-contain bg-[url(@/assets/cockpit/bg/标题.png)] flex items-center justify-center"
      >
        <div class="text-white text-3xl font-bold ml-[120px]">智慧新城数据驾驶舱</div>
      </div>

      <!-- 顶部菜单 -->
      <div
        class="w-[504px] h-[61px] mb-[20px] bg-no-repeat bg-contain flex items-center justify-center bg-[url(@/assets/cockpit/bg/menu.png)]"
      >
        <el-radio-group v-model="modeType" size="large" class="mode-switch mt-[5px]">
          <el-radio-button label="commercial">商业模式</el-radio-button>
          <el-radio-button label="residential">居住模式</el-radio-button>
        </el-radio-group>
      </div>
      <!-- 返回后台 -->
      <div class="back-button absolute right-[10px] top-[25px]" @click="back">
        <div class="back-icon">←</div>
        <span class="back-text">返回后台</span>
      </div>
    </div>

    <!-- 页面内容区 -->
    <div class="content w-full h-[calc(100%-78px)] relative flex justify-between overflow-hidden mt-[-30px]">
      <!-- 地图 -->
      <div class="map-container w-[100%] h-[100%]">
        <AMapComponent ref="amap" :center="mapCenter" :zoom="16" mapStyle="amap://styles/blue" @mapInit="handleMapInit" />
      </div>
      <!-- 左侧内容区 -->
      <div class="left-content z-2 w-[618px] h-[1001px] absolute top-0 left-0">
        <!-- 商业模式左侧图表 -->
        <div v-if="modeType === 'commercial'">
          <!-- 企业数量及增长率 -->
          <Chart1 />
           <!-- 楼栋去化率统计 -->
          <Chart7 />
          <!-- 楼栋企业增长趋势 -->
          <Chart3 />
        </div>
        <!-- 居住模式左侧图表 -->
        <div v-if="modeType === 'residential'">
          <!-- 小区人口数量及增长率 -->
          <ResidentChart1 />
          <!-- 小区居住区分布 -->
          <ResidentChart2 />
          <!-- 居民用电趋势 -->
          <ResidentChart7 />
        </div>
      </div>

      <!-- 右侧内容区 -->
      <div class="right-content z-2 w-[618px] h-[1001px] absolute top-0 right-0">
        <!-- 商业模式右侧图表 -->
        <div v-if="modeType === 'commercial'">
          <!-- 税收及增长率 -->
          <Chart4 />
          <!-- 本年入驻企业分布 -->
          <Chart2 />
          <!-- 2024年月度用电趋势 -->
          <Chart8 />
         
        </div>
        <!-- 居住模式右侧图表 -->
        <div v-if="modeType === 'residential'">
          <!-- 公寓人口数量及增长率 -->
          <ResidentChart4 />
          <!-- 公寓居住分布 -->
          <ResidentChart5 />
          <!-- 移动用户数趋势 -->
          <ResidentChart8 />
        </div>
      </div>
    </div>

    <!-- 楼宇详情弹窗 -->
    <DialogComponent :visible="dialogVisible" :building-data="selectedBuilding" @close="closeDialog" />

    <!-- 小区详情弹窗 -->
    <ResidentialDialogComponent
      :visible="residentialDialogVisible"
      :residential-data="selectedResidential"
      @close="closeResidentialDialog"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, provide } from "vue";
import { ElRadioButton, ElRadioGroup } from "element-plus";
import AMapComponent from "@/components/AMapComponent/index.vue";
import DialogComponent from "./components/DialogComponent.vue";
import ResidentialDialogComponent from "./components/ResidentialDialogComponent.vue";
import autofit from "autofit.js";
import { listCommunity } from "@/api/community/community";
import { listBuilding } from "@/api/building/building";

// 导入图表组件
import Chart1 from "./components/Chart1.vue";
import Chart2 from "./components/Chart2.vue";
import Chart3 from "./components/Chart3.vue";
import Chart4 from "./components/Chart4.vue";
import Chart5 from "./components/Chart5.vue";
import Chart7 from "./components/Chart7.vue";
import Chart8 from "./components/Chart8.vue";
import ResidentChart1 from "./components/ResidentChart1.vue";
import ResidentChart2 from "./components/ResidentChart2.vue";
import ResidentChart3 from "./components/ResidentChart3.vue";
import ResidentChart4 from "./components/ResidentChart4.vue";
import ResidentChart5 from "./components/ResidentChart5.vue";
import ResidentChart6 from "./components/ResidentChart6.vue";
import ResidentChart7 from "./components/ResidentChart7.vue";
import ResidentChart8 from "./components/ResidentChart8.vue";

// 当前模式类型
const modeType = ref("commercial");

// 地图实例
const amap = ref(null);
const mapCenter = ref([113.119924, 22.967078]);
const buildingMarkers = ref([]);

// 楼宇数据
const buildingsList = ref([]);
// 小区数据
const communityList = ref([]);

// 向子组件提供楼宇数据
provide('buildingsList', buildingsList);

// 对话框相关
const dialogVisible = ref(false);
const selectedBuilding = ref(null);
const residentialDialogVisible = ref(false);
const selectedResidential = ref(null);

// 获取楼宇列表数据
const fetchBuildingsList = async () => {
  try {
    const response = await listBuilding({
      pageSize: 9999,
      page: 1,
    });

    if (response.code === 200 && response.rows) {
      buildingsList.value = response.rows;
      console.log("获取楼宇数据成功:", buildingsList.value);

      // 如果地图已初始化且当前是商业模式，更新地图显示
      if (amap.value && amap.value.map && modeType.value === "commercial") {
        updateMapByMode("commercial");
      }
    } else {
      console.error("获取楼宇数据失败:", response.msg);
    }
  } catch (error) {
    console.error("获取楼宇数据异常:", error);
  }
};

// 获取小区列表数据
const fetchCommunityList = async () => {
  try {
    const response = await listCommunity({
      pageSize: 9999,
      page: 1,
    });

    if (response.code === 200 && response.rows) {
      communityList.value = response.rows;
      console.log("获取小区数据成功:", communityList.value);

      // 如果地图已初始化且当前是住宅模式，更新地图显示
      if (map.value && mode.value === "residential") {
        updateMapMarkers();
      }
    } else {
      console.error("获取小区数据失败:", response.msg);
    }
  } catch (error) {
    console.error("获取小区数据异常:", error);
  }
};

// 将楼宇数据转换为地图标记格式
const getBuildingsForMap = () => {
  const data = modeType.value === "commercial" ? buildingsList.value : communityList.value;
  return data
    .filter((building) => building.longitude && building.latitude)
    .map((building) => ({
      id: building.id,
      name: building.name,
      position: [parseFloat(building.longitude), parseFloat(building.latitude)],
      data: building, // 保存完整的楼宇数据
    }));
};

// 监听模式变化
watch(modeType, (newMode) => {
  console.log(`切换到${newMode === "commercial" ? "商业" : "居住"}模式`);
  // 根据模式变化更新地图显示
  updateMapByMode(newMode);
});

// 根据模式更新地图显示
const updateMapByMode = (mode) => {
  if (!amap.value || !amap.value.map) return;

  // 清除所有标记
  clearAllMarkers();

  if (mode === "commercial") {
    // 商业模式：显示楼宇标记
    addBuildingMarkers("commercial");
  } else {
    // 居住模式：显示小区标记和设备标记
    addBuildingMarkers("residential");
  }
};

// 清除所有标记
const clearAllMarkers = () => {
  // 清除楼宇标记
  if (buildingMarkers.value.length > 0) {
    buildingMarkers.value.forEach((marker) => {
      amap.value.map.remove(marker);
    });
    buildingMarkers.value = [];
  }
};

onMounted(async () => {
  // 初始化时间显示
  initTime();
  // 用了默认参数，等价于 autofit.init()
  autofit.init({
    dh: 1080,
    dw: 1920,
    el: "#cockpit",
    resize: true,
  });

  // 获取楼宇数据和社区数据
  await Promise.all([fetchBuildingsList(), fetchCommunityList()]);
});

onUnmounted(() => {
  timeInterval && clearInterval(timeInterval);
});

// 初始化时间显示
const initTime = () => {
  const updateTime = () => {
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const seconds = date.getSeconds().toString().padStart(2, "0");
    const week = ["星期天", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"][date.getDay()];
    document.getElementById("time").innerHTML = `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds} ${week}`;
  };

  updateTime();
  const timeInterval = setInterval(updateTime, 1000);
};

// 处理地图初始化完成事件
const handleMapInit = (mapInstance) => {
  console.log("地图初始化完成", mapInstance);

  // 添加交通图层
  const trafficLayer = new window.AMap.TileLayer.Traffic();
  mapInstance.add(trafficLayer);
  const satelliteLayer = new window.AMap.TileLayer.Satellite()
  mapInstance.add(satelliteLayer)

  // 根据当前模式添加标记点
  updateMapByMode(modeType.value);
};

// 添加楼宇标记（通用方法）
const addBuildingMarkers = (mode = "commercial") => {
  if (!amap.value || !amap.value.map || !window.AMap) return;

  const mapInstance = amap.value.map;

  // 根据模式获取不同的数据
  const data = getBuildingsForMap();
  console.log("添加楼宇标记数据:", data);

  const iconImage =
    mode === "commercial"
      ? "https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png"
      : "https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png";

  data.forEach((item) => {
    // 创建标记
    const marker = new window.AMap.Marker({
      position: item.position,
      title: item.name,
      icon: new window.AMap.Icon({
        image: iconImage,
        size: new window.AMap.Size(32, 32),
        imageSize: new window.AMap.Size(32, 32),
      }),
      offset: new window.AMap.Pixel(-16, -32),
      extData: item,
    });

    // 添加点击事件
    if (mode === "commercial") {
      marker.on("click", handleBuildingClick);
    } else {
      marker.on("click", handleResidentialClick);
    }

    // 添加到地图
    mapInstance.add(marker);
    buildingMarkers.value.push(marker);

    // 添加文本标记（只显示title）
    const text = new window.AMap.Text({
      text: item.name,
      position: item.position,
      offset: new window.AMap.Pixel(0, -45),
      style: {
        padding: "5px 10px",
        "background-color": "rgba(0,61,122,0.7)",
        "border-color": "rgba(0,149,255,0.5)",
        color: "white",
        "border-radius": "3px",
        "border-width": "1px",
        "box-shadow": "0 2px 6px 0 rgba(0, 0, 0, .3)",
        "text-align": "center",
        "font-size": "12px",
        lineHeight: "14px",
        cursor: "pointer",
      },
      extData: item,
    });

    // 为文本标记也添加点击事件
    if (mode === "commercial") {
      text.on("click", handleBuildingClick);
    } else {
      text.on("click", handleResidentialClick);
    }

    mapInstance.add(text);
    buildingMarkers.value.push(text);
  });
};

// 处理楼宇点击事件
const handleBuildingClick = (e) => {
  const building = e.target.getExtData().data;
  selectedBuilding.value = building;
  dialogVisible.value = true;
};

// 处理小区点击事件
const handleResidentialClick = (e) => {
  const residential = e.target.getExtData().data;
  selectedResidential.value = residential;
  residentialDialogVisible.value = true;
};

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

// 关闭小区对话框
const closeResidentialDialog = () => {
  residentialDialogVisible.value = false;
};

// 返回后台
const back = () => {
  window.location.href = "/";
};
</script>

<style scoped>
/* 特定样式，不能用Tailwind实现的部分 */
.dashboard-container {
  font-family: PingFangSC-Light, 微软雅黑;
  color: #fff;
}

.header {
  background-position-x: 30px;
}

/* 自定义Radio按钮组样式 - 直接覆盖Element Plus样式 */
.mode-switch :deep(.el-radio-button__inner) {
  background-color: rgba(0, 37, 84, 0.7) !important;
  border: 1px solid rgba(0, 149, 255, 0.5) !important;
  color: #ffffff !important;
  transition: all 0.3s;
}

.mode-switch :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background-color: rgba(0, 149, 255, 0.8) !important;
  color: #ffffff !important;
  box-shadow: -1px 0 0 0 transparent !important;
  font-weight: bold;
  border-color: rgba(0, 149, 255, 0.8) !important;
}

.mode-switch :deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-left: 1px solid rgba(0, 149, 255, 0.5) !important;
}

.mode-switch :deep(.el-radio-button:last-child .el-radio-button__inner) {
  border-right: 1px solid rgba(0, 149, 255, 0.5) !important;
}

/* 返回后台按钮样式 */
.back-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(0, 29, 88, 0.9) 0%, rgba(0, 37, 84, 0.95) 50%, rgba(0, 29, 88, 0.9) 100%);
  border: 1px solid rgba(0, 149, 255, 0.6);
  border-radius: 6px;
  color: #00feff;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  box-shadow: 0 2px 8px rgba(0, 149, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  text-shadow: 0 0 6px rgba(0, 254, 255, 0.3);
}

.back-icon {
  font-size: 16px;
  font-weight: bold;
  color: #00feff;
  transition: transform 0.3s ease;
}

.back-text {
  white-space: nowrap;
  font-size: 13px;
}

.back-button:hover {
  background: linear-gradient(135deg, rgba(0, 37, 84, 0.95) 0%, rgba(0, 149, 255, 0.3) 50%, rgba(0, 37, 84, 0.95) 100%);
  border-color: rgba(0, 254, 255, 0.8);
  box-shadow: 0 4px 12px rgba(0, 149, 255, 0.4), 0 0 20px rgba(0, 254, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.back-button:hover .back-icon {
  transform: translateX(-2px);
}

.back-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 149, 255, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}
</style>
