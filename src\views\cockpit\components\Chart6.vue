<template>
  <div
    class="w-[618px] h-[314px] p-[20px] mt-1 relative bg-no-repeat bg-contain bg-[url(@/assets/cockpit/bg/border.png)]">
    <div class="w-[200px] h-[30px] leading-[30px] text-white text-lg mb-3 pl-2 flex items-center">
      <span class="w-[4px] h-[18px] bg-[#00feff] mr-2 inline-block"></span>
      税收前十排行
    </div>
    <div ref="chartRef" class="w-full h-[231px]"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts";

// 图表引用
const chartRef = ref(null);
let chartInstance = null;

onMounted(() => {
  initChart();
  // 添加窗口大小变化的监听，自动调整图表大小
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  // 清除图表实例
  chartInstance && chartInstance.dispose();
  // 移除窗口大小变化的监听
  window.removeEventListener('resize', handleResize);
});

// 处理窗口大小变化，调整图表大小
const handleResize = () => {
  chartInstance && chartInstance.resize();
};

// 初始化图表
const initChart = () => {
  // 检查DOM元素是否存在
  if (!chartRef.value) return;

  // 如果图表实例已存在，则销毁
  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartRef.value, "macarons");

  // 模拟企业税收排行数据
  const companies = [
    '国科控股集团',
    '联想控股',
    '京东集团',
    '百度科技',
    '中关村发展',
    '首开股份',
    '华熙生物',
    '商汤科技',
    '字节跳动',
    '旷视科技'
  ];
  
  const taxData = [
    320,
    302,
    285,
    265,
    240,
    225,
    198,
    185,
    175,
    160
  ];

  // 反转数组以便从下到上显示
  const reversedCompanies = [...companies].reverse();
  const reversedTaxData = [...taxData].reverse();

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}万元',
      backgroundColor: 'rgba(10, 30, 50, 0.7)',
      borderColor: 'rgba(48, 207, 208, 0.3)',
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      top: '3%',
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '税收(万元)',
      nameTextStyle: {
        color: '#fff',
        fontSize: 12
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(26, 92, 215, 0.3)',
          type: 'dashed'
        }
      },
      axisLine: {
        lineStyle: {
          color: '#1a5cd7'
        }
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'category',
      data: reversedCompanies,
      axisTick: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: '#1a5cd7'
        }
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      }
    },
    series: [
      {
        name: '税收',
        type: 'bar',
        data: reversedTaxData,
        barWidth: 12,
        label: {
          show: true,
          position: 'right',
          color: '#fff',
          fontSize: 12,
          formatter: '{c}万元'
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            1, 0, 0, 0,
            [
              { offset: 0, color: '#00feff' },
              { offset: 1, color: '#027eff' }
            ]
          ),
          borderRadius: [0, 4, 4, 0]
        }
      }
    ]
  };

  chartInstance.setOption(option);
};
</script>

<style scoped>
/* 可以添加组件特有的样式 */
</style> 