<template>
  <div
    class="w-[618px] h-[314px] p-[20px] mt-1 relative bg-no-repeat bg-contain bg-[url(@/assets/cockpit/bg/border.png)]">
    <div class="w-[200px] h-[30px] leading-[30px] text-white text-lg mb-3 pl-2 flex items-center">
      <span class="w-[4px] h-[18px] bg-[#00feff] mr-2 inline-block"></span>
      居民用电趋势
    </div>
    <div ref="chartRef" class="w-full h-[231px]"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts";
import { getResidentElectricityTrend } from "@/api/cockpit/index";

// 图表引用
const chartRef = ref(null);
let chartInstance = null;

// 图表数据 - 参考 Chart8 格式
const chartData = ref({
  title: '居民用电趋势',
  xis: [],
  data: [],
  rate: []
});

onMounted(() => {
  fetchData();
  // 添加窗口大小变化的监听，自动调整图表大小
  window.addEventListener('resize', handleResize);
});

// 获取数据
const fetchData = async () => {
  try {
    const response = await getResidentElectricityTrend();
    if (response.code === 200) {
      chartData.value = response.data;
      initChart();
    } else {
      console.error('获取居民用电趋势数据失败:', response.msg);
    }
  } catch (error) {
    console.error('获取居民用电趋势数据异常:', error);
  }
};

onUnmounted(() => {
  // 清除图表实例
  chartInstance && chartInstance.dispose();
  // 移除窗口大小变化的监听
  window.removeEventListener('resize', handleResize);
});

// 处理窗口大小变化，调整图表大小
const handleResize = () => {
  chartInstance && chartInstance.resize();
};

// 初始化图表
const initChart = () => {
  // 检查DOM元素是否存在
  if (!chartRef.value) return;

  // 如果图表实例已存在，则销毁
  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartRef.value, "macarons");

  // 使用动态数据 - 参考 Chart8 格式
  const months = chartData.value.xis;
  const electricityData = chartData.value.data;
  const growthRateData = chartData.value.rate.map(item=>item.toFixed(1));

  // 使用紫色系渐变色（参考ResidentChart3的颜色配置）
  const barColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#8e79f5' },
    { offset: 1, color: '#6c5dd3' }
  ]);

  const lineColor = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
    { offset: 0, color: '#ba7fec' },
    { offset: 1, color: '#9d4de0' }
  ]);

  // 创建图表配置
  const option = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: function (params) {
        let result = `${params[0].axisValue}<br/>`;

        params.forEach(param => {
          if (param.seriesType === 'bar') {
            result += `<span style="color: #8e79f5;">● 用电量: ${param.value}万千瓦时</span><br/>`;
          } else if (param.seriesType === 'line') {
            result += `<span style="color: #ba7fec;">● 同比增长: ${param.value}%</span><br/>`;
          }
        });

        return result;
      },
      backgroundColor: 'rgba(10, 30, 50, 0.7)',
      borderColor: 'rgba(48, 207, 208, 0.3)',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      data: ['总用电量', '同比增长率'],
      textStyle: {
        color: '#fff'
      },
      selectedMode: true,
      selected: {
        '总用电量': true,
        '同比增长率': true
      }
    },
    grid: {
      left: "4%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: months,
        axisPointer: {
          type: 'shadow'
        },
        axisTick: {
          alignWithLabel: true,
          show: false
        },
        axisLine: {
          lineStyle: {
            color: '#1a5cd7'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 12
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '用电量',
        nameTextStyle: {
          color: '#fff',
          fontSize: 12,
          align: 'right'
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(26, 92, 215, 0.3)',
            type: 'dashed'
          }
        },
        axisLine: {
          lineStyle: {
            color: '#1a5cd7'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 12,
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '增长率(%)',
        nameTextStyle: {
          color: '#fff',
          fontSize: 12,
          align: 'left'
        },
        splitLine: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: '#1a5cd7'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 12,
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '总用电量',
        type: 'bar',
        barWidth: 20,
        data: electricityData,
        itemStyle: {
          color: barColor
        }
      },
      {
        name: '同比增长率',
        type: 'line',
        smooth: true,
        yAxisIndex: 1,
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#ba7fec'
        },
        lineStyle: {
          width: 2,
          color: lineColor
        },
        data: growthRateData,
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%',
          color: '#ba7fec',
          fontSize: 12
        }
      }
    ]
  };

  chartInstance.setOption(option);
};
</script>

<style scoped>
/* 可以添加组件特有的样式 */
</style>
