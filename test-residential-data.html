<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小区数据预览</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #001d58 0%, #002554 100%);
            font-family: 'Microsoft YaHei', sans-serif;
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            color: #00feff;
            text-shadow: 0 0 10px rgba(0, 254, 255, 0.5);
            text-align: center;
            margin-bottom: 30px;
        }
        
        .residential-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .residential-card {
            background: linear-gradient(135deg, 
                rgba(0, 29, 88, 0.8) 0%, 
                rgba(0, 37, 84, 0.9) 100%);
            border: 1px solid rgba(0, 149, 255, 0.3);
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .residential-card:hover {
            border-color: rgba(0, 149, 255, 0.6);
            box-shadow: 0 0 25px rgba(0, 149, 255, 0.2);
            transform: translateY(-5px);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0, 149, 255, 0.2);
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #00feff;
            text-shadow: 0 0 8px rgba(0, 254, 255, 0.3);
        }
        
        .card-group {
            background: rgba(0, 149, 255, 0.2);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: #00feff;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
        }
        
        .label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 13px;
        }
        
        .value {
            color: #00feff;
            font-weight: 500;
            font-size: 13px;
        }
        
        .value.highlight {
            color: #00ff88;
            font-weight: 600;
        }
        
        .value.occupancy {
            color: #ffa726;
            font-weight: 600;
        }
        
        .value.positive {
            color: #00ff88;
        }
        
        .value.negative {
            color: #ff6b6b;
        }
        
        .back-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 20px;
            background: linear-gradient(135deg, rgba(0, 149, 255, 0.8) 0%, rgba(0, 254, 255, 0.6) 100%);
            border: 1px solid rgba(0, 149, 255, 0.5);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: linear-gradient(135deg, rgba(0, 149, 255, 1) 0%, rgba(0, 254, 255, 0.8) 100%);
            box-shadow: 0 0 20px rgba(0, 149, 255, 0.4);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="test-dialog.html" class="back-btn">← 返回测试页面</a>
        
        <h1>🏘️ 小区数据预览</h1>
        
        <div class="residential-grid" id="residentialGrid">
            <!-- 数据将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        // 小区数据（与Vue组件中的数据保持一致）
        const residentialData = [
            {
                id: 'residential_1',
                name: '阳光花园小区',
                group: 'A组',
                totalBuildings: 12,
                propertyData: '已提供',
                totalHouseholds: 1200,
                residentHouseholds: 980,
                nonResidentHouseholds: 220,
                decoratedNonResident: 180,
                undecorated: 40,
                residentPopulation: 2940,
                populationChange: 15,
                householdChange: 8,
                occupancyRate: 81.7
            },
            {
                id: 'residential_2',
                name: '绿城桂花园',
                group: 'B组',
                totalBuildings: 18,
                propertyData: '已提供',
                totalHouseholds: 1800,
                residentHouseholds: 1520,
                nonResidentHouseholds: 280,
                decoratedNonResident: 240,
                undecorated: 40,
                residentPopulation: 4560,
                populationChange: 22,
                householdChange: 12,
                occupancyRate: 84.4
            },
            {
                id: 'residential_3',
                name: '万科城市花园',
                group: 'A组',
                totalBuildings: 15,
                propertyData: '已提供',
                totalHouseholds: 1500,
                residentHouseholds: 1280,
                nonResidentHouseholds: 220,
                decoratedNonResident: 190,
                undecorated: 30,
                residentPopulation: 3840,
                populationChange: 18,
                householdChange: 10,
                occupancyRate: 85.3
            },
            {
                id: 'residential_4',
                name: '保利香槟国际',
                group: 'C组',
                totalBuildings: 8,
                propertyData: '已提供',
                totalHouseholds: 800,
                residentHouseholds: 720,
                nonResidentHouseholds: 80,
                decoratedNonResident: 70,
                undecorated: 10,
                residentPopulation: 2160,
                populationChange: 5,
                householdChange: 3,
                occupancyRate: 90.0
            },
            {
                id: 'residential_5',
                name: '中海滨湖公馆',
                group: 'B组',
                totalBuildings: 20,
                propertyData: '已提供',
                totalHouseholds: 2000,
                residentHouseholds: 1650,
                nonResidentHouseholds: 350,
                decoratedNonResident: 300,
                undecorated: 50,
                residentPopulation: 4950,
                populationChange: 28,
                householdChange: 15,
                occupancyRate: 82.5
            }
        ];

        function formatNumber(num) {
            return Number(num).toLocaleString();
        }

        function formatChange(change) {
            const prefix = change > 0 ? '+' : '';
            return `${prefix}${change}`;
        }

        function getChangeClass(change) {
            return change > 0 ? 'positive' : change < 0 ? 'negative' : '';
        }

        function renderResidentialCards() {
            const grid = document.getElementById('residentialGrid');
            
            residentialData.forEach(residential => {
                const card = document.createElement('div');
                card.className = 'residential-card';
                
                card.innerHTML = `
                    <div class="card-header">
                        <span class="card-title">🏘️ ${residential.name}</span>
                        <span class="card-group">${residential.group}</span>
                    </div>
                    
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">总栋数</span>
                            <span class="value">${residential.totalBuildings}栋</span>
                        </div>
                        <div class="info-item">
                            <span class="label">物管数据</span>
                            <span class="value">${residential.propertyData}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">住宅总户数</span>
                            <span class="value highlight">${formatNumber(residential.totalHouseholds)}户</span>
                        </div>
                        <div class="info-item">
                            <span class="label">常住户数</span>
                            <span class="value">${formatNumber(residential.residentHouseholds)}户</span>
                        </div>
                        <div class="info-item">
                            <span class="label">不常住户数</span>
                            <span class="value">${formatNumber(residential.nonResidentHouseholds)}户</span>
                        </div>
                        <div class="info-item">
                            <span class="label">已装修不常住</span>
                            <span class="value">${formatNumber(residential.decoratedNonResident)}户</span>
                        </div>
                        <div class="info-item">
                            <span class="label">未装修户数</span>
                            <span class="value">${formatNumber(residential.undecorated)}户</span>
                        </div>
                        <div class="info-item">
                            <span class="label">常住总人数</span>
                            <span class="value highlight">${formatNumber(residential.residentPopulation)}人</span>
                        </div>
                        <div class="info-item">
                            <span class="label">人数变量</span>
                            <span class="value ${getChangeClass(residential.populationChange)}">${formatChange(residential.populationChange)}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">户数变量</span>
                            <span class="value ${getChangeClass(residential.householdChange)}">${formatChange(residential.householdChange)}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">入住率</span>
                            <span class="value occupancy">${residential.occupancyRate}%</span>
                        </div>
                    </div>
                `;
                
                grid.appendChild(card);
            });
        }

        // 页面加载完成后渲染数据
        document.addEventListener('DOMContentLoaded', renderResidentialCards);
    </script>
</body>
</html>
