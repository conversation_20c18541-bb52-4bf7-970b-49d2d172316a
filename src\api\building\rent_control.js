import request from '@/utils/request'
// 查询租控图列表
export function listRentControl(query) {
  return request({
    url: '/building/rent_control/list',
    method: 'get',
    params: query
  })
}

// 查询租控图详细
export function getRentControl(id) {
  return request({
    url: '/building/rent_control/' + id,
    method: 'get'
  })
}

// 新增租控图
export function addRentControl(data) {
  return request({
    url: '/building/rent_control/add',
    method: 'post',
    data: data
  })
}

// 修改租控图
export function updateRentControl(data) {
  return request({
    url: '/building/rent_control/edit',
    method: 'post',
    data: data
  })
}

// 删除租控图
export function delRentControl(id) {
  return request({
    url: '/building/rent_control/' + id,
    method: 'delete'
  })
}

// 获取租控图
export function getBuildingRentControl(query) {
  return request({
    url: '/building/rent_control/getRentControl',
    method: 'get',
    params: query
  })
}

//批量更新租控单位
export function batchUpdateRentControl(data) {
  return request({
    url: '/building/rent_control/batchUpdateRentControl',
    method: 'post',
    data: data
  })
}

