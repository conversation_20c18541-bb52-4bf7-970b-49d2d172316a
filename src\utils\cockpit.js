import * as echarts from "echarts";

/**
 * 生成指定数量的独特渐变色
 * @param {number} count - 需要生成的颜色数量
 * @returns {Array} ECharts兼容的LinearGradient颜色数组
 */
export function generateUniqueColors(count) {
  const colors = [];

  // 确保至少生成1种颜色
  if (count <= 0) {
    count = 1;
  }

  // 色相步长，确保颜色在色环上均匀分布
  const hueStep = 360 / count;

  for (let i = 0; i < count; i++) {
    // 计算色相值，均匀分布在0-360度
    const hue = (i * hueStep) % 360;

    // 饱和度变化：70%, 80%, 90% 循环，增加视觉层次
    const saturation = 70 + (i % 3) * 10;

    // 起始亮度：45%-60% 变化
    const lightness1 = 45 + (i % 4) * 5;

    // 结束亮度：25%-40% 变化，确保渐变效果
    const lightness2 = 25 + (i % 4) * 5;

    // 创建垂直渐变（从上到下）
    const gradient = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: `hsl(${hue}, ${saturation}%, ${lightness1}%)`
      },
      {
        offset: 1,
        color: `hsl(${hue}, ${saturation}%, ${lightness2}%)`
      }
    ]);

    colors.push(gradient);
  }

  return colors;
}

/**
 * 获取预定义的科技感渐变色系列（兼容性函数）
 * @returns {Array} 10种预定义的科技感渐变色
 */
export function getTechColors() {
  return [
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#00feff' },
      { offset: 1, color: '#027eff' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#4facfe' },
      { offset: 1, color: '#00f2fe' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#667eea' },
      { offset: 1, color: '#764ba2' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#f83600' },
      { offset: 1, color: '#f9d423' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#43e97b' },
      { offset: 1, color: '#38f9d7' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#fa709a' },
      { offset: 1, color: '#fee140' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#30cfd0' },
      { offset: 1, color: '#5472d2' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#a1c4fd' },
      { offset: 1, color: '#c2e9fb' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#ff9a9e' },
      { offset: 1, color: '#fad0c4' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#ebc0fd' },
      { offset: 1, color: '#d9ded8' }
    ])
  ];
}