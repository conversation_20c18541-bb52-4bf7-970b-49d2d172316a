import request from '@/utils/request'
// 查询企业信息更新说明列表
export function listEnterpriseExplanation(query) {
  return request({
    url: '/enterprise/enterprise_explanation/list',
    method: 'get',
    params: query
  })
}

// 查询企业信息更新说明详细
export function getEnterpriseExplanation(id) {
  return request({
    url: '/enterprise/enterprise_explanation/' + id,
    method: 'get'
  })
}

// 新增企业信息更新说明
export function addEnterpriseExplanation(data) {
  return request({
    url: '/enterprise/enterprise_explanation/add',
    method: 'post',
    data: data
  })
}

// 修改企业信息更新说明
export function updateEnterpriseExplanation(data) {
  return request({
    url: '/enterprise/enterprise_explanation/edit',
    method: 'post',
    data: data
  })
}

// 删除企业信息更新说明
export function delEnterpriseExplanation(id) {
  return request({
    url: '/enterprise/enterprise_explanation/' + id,
    method: 'delete'
  })
}

