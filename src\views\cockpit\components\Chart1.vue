<template>
  <div
    class="w-[618px] h-[314px] p-[20px] mt-1 relative bg-no-repeat bg-contain bg-[url(@/assets/cockpit/bg/border.png)]">
    <div class="w-[200px] h-[30px] leading-[30px] text-white text-lg mb-3 pl-2 flex items-center">
      <span class="w-[4px] h-[18px] bg-[#00feff] mr-2 inline-block"></span>
      {{chartData.title }}
    </div>
    <div ref="chartRef" class="w-full h-[231px]"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts";
import { getEnterprisesGrowth } from "@/api/cockpit/index";

// 图表引用
const chartRef = ref(null);
let chartInstance = null;

// 图表数据
const chartData = ref({
  title: '企业数量及增长率',
  xis: [],
  data: [],
  rate: []
});

onMounted(() => {
  fetchData();
  // 添加窗口大小变化的监听，自动调整图表大小
  window.addEventListener('resize', handleResize);
});

// 获取数据
const fetchData = async () => {
  try {
    const response = await getEnterprisesGrowth();
    if (response.code === 200) {
      chartData.value = response.data;
      initChart();
    } else {
      console.error('获取企业增长数据失败:', response.msg);
    }
  } catch (error) {
    console.error('获取企业增长数据异常:', error);
  }
};

onUnmounted(() => {
  // 清除图表实例
  chartInstance && chartInstance.dispose();
  // 移除窗口大小变化的监听
  window.removeEventListener('resize', handleResize);
});

// 处理窗口大小变化，调整图表大小
const handleResize = () => {
  chartInstance && chartInstance.resize();
};

// 初始化图表
const initChart = () => {
  // 检查DOM元素是否存在
  if (!chartRef.value) return;

  // 如果图表实例已存在，则销毁
  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartRef.value, "macarons");

  // 使用动态数据
  const years = chartData.value.xis.map(item=>`${item}年`);
  const enterpriseCount = chartData.value.data;
  const growthRate = chartData.value.rate;

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['企业数量', '增长率'],
      textStyle: {
        color: '#fff'
      },
      selectedMode: true,
      selected: {
        '企业数量': true,
        '增长率': true
      }
    },
    grid: {
      left: '4%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: years,
        axisPointer: {
          type: 'shadow'
        },
        axisTick: {
          alignWithLabel: true,
          show: false
        },
        axisLine: {
          lineStyle: {
            color: '#1a5cd7'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 12
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '企业数量',
        min: 0,
        nameTextStyle: {
          color: '#fff',
          fontSize: 12,
          align: 'right'
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(26, 92, 215, 0.3)',
            type: 'dashed'
          }
        },
        axisLine: {
          lineStyle: {
            color: '#1a5cd7'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 12,
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '增长率(%)',
        min: 0,
        nameTextStyle: {
          color: '#fff',
          fontSize: 12,
          align: 'left'
        },
        splitLine: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: '#1a5cd7'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 12,
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '企业数量',
        type: 'bar',
        barWidth: 20,
        data: enterpriseCount,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0, 0, 0, 1,
            [
              { offset: 0, color: '#00feff' },
              { offset: 1, color: '#027eff' }
            ]
          )
        }
      },
      {
        name: '增长率',
        type: 'line',
        smooth: true,
        yAxisIndex: 1,
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#ff9933'
        },
        lineStyle: {
          width: 2,
          color: new echarts.graphic.LinearGradient(
            0, 0, 1, 0,
            [
              { offset: 0, color: '#ffcc33' },
              { offset: 1, color: '#ff6600' }
            ]
          )
        },
        data: growthRate,
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%',
          color: '#ff9933',
          fontSize: 12
        }
      }
    ]
  };

  chartInstance.setOption(option);
};
</script>

<style scoped>
/* 可以添加组件特有的样式 */
</style> 