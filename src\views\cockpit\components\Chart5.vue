<template>
  <div
    class="w-[618px] h-[314px] p-[20px] mt-1 relative bg-no-repeat bg-contain bg-[url(@/assets/cockpit/bg/border.png)]">
    <div class="w-[200px] h-[30px] leading-[30px] text-white text-lg mb-3 pl-2 flex items-center">
      <span class="w-[4px] h-[18px] bg-[#00feff] mr-2 inline-block"></span>
      税收行业占比
    </div>
    <div ref="chartRef" class="w-full h-[231px]"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts";

// 图表引用
const chartRef = ref(null);
let chartInstance = null;

onMounted(() => {
  initChart();
  // 添加窗口大小变化的监听，自动调整图表大小
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  // 清除图表实例
  chartInstance && chartInstance.dispose();
  // 移除窗口大小变化的监听
  window.removeEventListener('resize', handleResize);
});

// 处理窗口大小变化，调整图表大小
const handleResize = () => {
  chartInstance && chartInstance.resize();
};

// 初始化图表
const initChart = () => {
  // 检查DOM元素是否存在
  if (!chartRef.value) return;

  // 如果图表实例已存在，则销毁
  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartRef.value, "macarons");

  // 行业税收数据
  const industries = ['信息技术', '金融服务', '商贸批发', '文化创意', '科技研发', '生活服务', '其他行业'];
  const taxAmount = [820, 750, 580, 484, 300, 220, 150];

  // 构建饼图数据
  const pieData = industries.map((name, index) => ({
    name: name,
    value: taxAmount[index]
  }));

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      position: 'right',
      backgroundColor: 'rgba(10, 30, 50, 0.7)',
      borderColor: 'rgba(48, 207, 208, 0.3)',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'middle',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 12,
      data: industries,
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      formatter: function (name) {
        // 找到对应的数据项
        const item = pieData.find(item => item.name === name);
        // 如果找到了数据项，返回带有数据的名称
        if (item) {
          // 控制显示数据长度，太长的数字可以简化显示
          const value = item.value > 1000 ? (item.value / 1000).toFixed(1) + 'k' : item.value;
          const percentage = Math.round(item.value / pieData.reduce((sum, current) => sum + current.value, 0) * 100);
          return `${name}: ${value} (${percentage}%)`;
        }
        // 如果没有找到数据项，只返回名称
        return name;
      }
    },
    series: [
      {
        name: '税收金额',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['30%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: 'rgba(10, 20, 30, 0.2)',
          borderWidth: 2
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: pieData,
        // 科技感渐变色系列
        color: [
          new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#43e97b' },
            { offset: 1, color: '#38f9d7' }
          ]),
          new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#00feff' },
            { offset: 1, color: '#027eff' }
          ]),
          new echarts.graphic.LinearGradient(0, 0, 1, 1, [
            { offset: 0, color: '#667eea' },
            { offset: 1, color: '#764ba2' }
          ]),
          new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#f83600' },
            { offset: 1, color: '#f9d423' }
          ]),
          new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#fa709a' },
            { offset: 1, color: '#fee140' }
          ]),
          new echarts.graphic.LinearGradient(0, 0, 1, 1, [
            { offset: 0, color: '#4facfe' },
            { offset: 1, color: '#00f2fe' }
          ]),
          new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#30cfd0' },
            { offset: 1, color: '#5472d2' }
          ])
        ]
      }
    ]
  };

  chartInstance.setOption(option);
};
</script>

<style scoped>
/* 可以添加组件特有的样式 */
</style> 