<template>
  <div
    class="w-[618px] h-[314px] p-[20px] mt-1 relative bg-no-repeat bg-contain bg-[url(@/assets/cockpit/bg/border.png)]">
    <div class="w-[200px] h-[30px] leading-[30px] text-white text-lg mb-3 pl-2 flex items-center">
      <span class="w-[4px] h-[18px] bg-[#00feff] mr-2 inline-block"></span>
      楼栋企业增长
    </div>
    <div ref="chartRef" class="w-full h-[231px]"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts";
import { generateUniqueColors } from "@/utils/cockpit";
import { getBuildingGrowthTrend } from "@/api/cockpit/index";

// 图表引用
const chartRef = ref(null);
let chartInstance = null;

// 图表数据
const chartData = ref({
  title: '楼栋企业增长',
  xis: ['2025年'],
  data: []
});

onMounted(() => {
  fetchData();
  // 添加窗口大小变化的监听，自动调整图表大小
  window.addEventListener("resize", handleResize);
});

// 获取数据
const fetchData = async () => {
  try {
    const response = await getBuildingGrowthTrend();
    if (response.code === 200) {
      chartData.value = response.data;
      chartData.value.xis = ['2025年']
      initChart();
    } else {
      console.error('获取楼栋企业增长趋势数据失败:', response.msg);
    }
  } catch (error) {
    console.error('获取楼栋企业增长趋势数据异常:', error);
  }
};

onUnmounted(() => {
  // 清除图表实例
  chartInstance && chartInstance.dispose();
  // 移除窗口大小变化的监听
  window.removeEventListener("resize", handleResize);
});

// 处理窗口大小变化，调整图表大小
const handleResize = () => {
  chartInstance && chartInstance.resize();
};

// 初始化图表
const initChart = () => {
  // 检查DOM元素是否存在
  if (!chartRef.value) return;

  // 如果图表实例已存在，则销毁
  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartRef.value, "macarons");

  // 不排序，保持原始顺序
  const sortedData = [...chartData.value.data];

  // 提取楼栋名称和企业数据
  const buildingNames = sortedData.map((item) => item.name);
  const enterpriseData = sortedData.map((item) => {
    // 如果value是数组，取第一个值；如果是单个值，直接使用
    return Array.isArray(item.value) ? item.value[0] : item.value;
  });

  // 使用公共函数生成48种独特颜色
  const colors = generateUniqueColors(chartData.value.data.length);

  // 创建垂直柱状图配置
  const option = {
    backgroundColor: "transparent",
    // 设置全局颜色系列，确保图例显示不同颜色
    color: colors,
    barCategoryGap: 0,
    tooltip: {
      trigger: "item",
      formatter: function (params) {
        // 确保参数存在且有有效值
        if (!params || params.value === null || params.value === undefined) {
          return "";
        }

        const buildingName = params.seriesName;
        const value = params.value;

        return `
          <div style="font-size: 12px;">
            <div style="font-weight: bold; margin-bottom: 4px;">${buildingName}</div>
            <div>企业数量: <span style="color: #00feff;">${value}</span></div>
          </div>
        `;
      },
      backgroundColor: "rgba(10, 30, 50, 0.7)",
      borderColor: "rgba(48, 207, 208, 0.3)",
      textStyle: {
        color: "#fff",
      },
    },
    legend: {
      type: "scroll",
      orient: "vertical",
      right: 8,
      top: 10,
      bottom: 20,
      width: 120, // 固定图例宽度
      data: buildingNames,
      textStyle: {
        color: "#fff",
        fontSize: 12,
        lineHeight: 13,
        width: 100, // 固定文字宽度
        overflow: "truncate", // 溢出截断
        ellipsis: "...", // 显示省略号
      },
      padding: [10, 0, 0, 0],
      // 自定义格式化函数，确保文字长度控制
      formatter: function (name) {
        if (name.length > 8) {
          return name.substring(0, 8) + "...";
        }
        return name;
      },
    },

    grid: {
      left: "1%", // 减少左边距
      right: "25%",
      bottom: "3%",
      top: "18%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: chartData.value.xis,
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: "#1a5cd7",
        },
      },
      axisLabel: {
        show: true,
        color: "#fff",
        fontSize: 12,
      },
      // 减少类别轴两侧的空白边距
      boundaryGap: false, // 左右各留5%的边距
    },
    yAxis: {
      type: "value",
      name: "企业数量",
      nameTextStyle: {
        color: "#fff",
        fontSize: 12,
      },
      axisLine: {
        lineStyle: {
          color: "#1a5cd7",
        },
      },
      axisLabel: {
        color: "#fff",
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: "rgba(26, 92, 215, 0.3)",
          type: "dashed",
        },
      },
    },
    series: buildingNames.map((name, index) => ({
      name: name,
      type: "bar",
      data: [enterpriseData[index]], // 每个系列只有一个数据点对应当前年份
      barGap: 0,
      emphasis: {
        focus: "series",
      },
    })),
  };

  chartInstance.setOption(option);
};
</script>

<style scoped>
/* 可以添加组件特有的样式 */
</style>
