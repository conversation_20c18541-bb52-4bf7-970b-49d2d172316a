<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="小区组别" prop="communityGroupId" label-width="170">
        <el-select v-model="queryParams.communityGroupId" placeholder="请选择小区组别" clearable style="width: 200px">
          <el-option v-for="dict in communitygroup" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="小区名称" prop="name" label-width="170">
        <el-input v-model="queryParams.name" placeholder="请输入小区名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="成熟小区" prop="isMature" label-width="170">
        <el-select v-model="queryParams.isMature" placeholder="请选择是否成熟小区" clearable style="width: 100px">
          <el-option v-for="dict in commonstatus" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="住宅" prop="hasDwelling" label-width="170">
        <el-select v-model="queryParams.hasDwelling" placeholder="请选择是否住宅" clearable style="width: 100px">
          <el-option v-for="dict in commonstatus" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="公寓" prop="hasApartment" label-width="170">
        <el-select v-model="queryParams.hasApartment" placeholder="请选择是否公寓" clearable style="width: 100px">
          <el-option v-for="dict in commonstatus" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['community:community:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['community:community:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['community:community:remove']"
          >删除</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['community:community:import']"
        >导入</el-button>
      </el-col>
<el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['community:community:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="communityList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="80" align="center" />
      <el-table-column label="序列" align="center" width="80">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="主键" align="center" prop="id" v-if="false" />
      <el-table-column label="组别" align="center" prop="communityGroupId" width="130">
        <template #default="scope"> <dict-tag :options="communitygroup" :value="scope.row.communityGroupId" /> </template>
      </el-table-column>
      <el-table-column label="小区名称" align="center" prop="name" width="250" />
      <el-table-column label="经度" align="center" prop="longitude" width="100" />
      <el-table-column label="纬度" align="center" prop="latitude" width="100" />
      <el-table-column label="总占地面积㎡" align="center" prop="coverArea" width="130" />
      <el-table-column label="总建筑面积㎡" align="center" prop="buildingArea" width="130" />
      <el-table-column label="住宅" align="center" prop="hasDwelling" width="100">
        <template #default="scope"> <dict-tag :options="commonstatus" :value="scope.row.hasDwelling" /> </template>
      </el-table-column>
      <el-table-column label="公寓" align="center" prop="hasApartment" width="100">
        <template #default="scope"> <dict-tag :options="commonstatus" :value="scope.row.hasApartment" /> </template>
      </el-table-column>
      <el-table-column label="成熟小区" align="center" prop="isMature" width="100">
        <template #default="scope"> <dict-tag :options="commonstatus" :value="scope.row.isMature" /> </template>
      </el-table-column>
      <el-table-column label="总栋数" align="center" prop="totalBuildings" width="100" />
      <el-table-column label="备注" align="center" prop="remark" width="250" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="130" fixed="right">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['community:community:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['community:community:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改小区信息对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="communityRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="小区组别" prop="communityGroupId" label-width="170">
              <el-select v-model="form.communityGroupId" placeholder="请输入小区组别" style="width: 280px">
                <el-option
                  v-for="dict in communitygroup"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成熟小区" prop="isMature" label-width="170">
              <el-select v-model="form.isMature" placeholder="请选择" style="width: 280px">
                <el-option
                  v-for="dict in commonstatus"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="小区名称" prop="name" label-width="170">
              <el-input v-model="form.name" placeholder="请输入小区名称" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总栋数" prop="totalBuildings" label-width="170">
              <el-input v-model="form.totalBuildings" placeholder="请输入总栋数" style="width: 280px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude" label-width="170">
              <el-input v-model="form.longitude" placeholder="请输入经度" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude" label-width="170">
              <el-input v-model="form.latitude" placeholder="请输入纬度" style="width: 280px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="总占地面积" prop="coverArea" label-width="170">
              <el-input v-model="form.coverArea" placeholder="请输入总占地面积" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总建筑面积" prop="buildingArea" label-width="170">
              <el-input v-model="form.buildingArea" placeholder="请输入总建筑面积" style="width: 280px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="公寓" prop="hasApartment" label-width="170">
              <el-select v-model="form.hasApartment" placeholder="请选择" style="width: 280px">
                <el-option
                  v-for="dict in commonstatus"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="住宅" prop="hasDwelling" label-width="170">
              <el-select v-model="form.hasDwelling" placeholder="请选择" style="width: 280px">
                <el-option
                  v-for="dict in commonstatus"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="25">
            <el-form-item label="备注" prop="remark" label-width="170">
              <el-input v-model="form.remark" placeholder="请输入地址" style="width: 770px" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip"><el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据</div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="community">
import { getToken } from "@/utils/auth";
import { listCommunity, getCommunity, delCommunity, addCommunity, updateCommunity } from "@/api/community/community";
const { proxy } = getCurrentInstance();
const { communitygroup, commonstatus } = proxy.useDict("communitygroup", "commonstatus");
const communityList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const daterangeCreateTime = ref([]);
const daterangeUpdateTime = ref([]);
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    isMature: null,
    communityGroupId: null,
    hasApartment: null,
    hasDwelling: null,
    name: null,
  },
  rules: ref({}),
});
const { queryParams, form, rules } = toRefs(data);

/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层
  open: false,
  // 弹出层标题（导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/community/community/importData",
}); /** 查询小区信息列表 */
function getList() {
  loading.value = true;
  listCommunity(queryParams.value).then((response) => {
    communityList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}
// 取消按钮
function cancel() {
  open.value = false;
  reset();
}
// 表单重置
function reset() {
  form.value = {
    id: null,
    communityGroupId: null,
    name: null,
    totalBuildings: null,
    isMature: null,
    remark: null,
  };
  proxy.resetForm("communityRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加小区信息";
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getCommunity(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改小区信息";
  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["communityRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateCommunity(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addCommunity(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm("是否确认删除选中的数据，此次将删除" + _ids.length + "条数据？")
    .then(function () {
      return delCommunity(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "community/community/export",
    {
      ...queryParams.value,
    },
    `community_${new Date().getTime()}.xlsx`
  );
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "小区信息导入";
  upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download("community/community/downTemplate", {}, `user_template_${new Date().getTime()}.xlsx`);
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}
getList();
</script>
