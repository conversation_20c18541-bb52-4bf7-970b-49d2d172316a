import request from '@/utils/request'
// 查询小区信息列表
export function listCommunity(query) {
  return request({
    url: '/community/community/list',
    method: 'get',
    params: query
  })
}

// 查询小区信息详细
export function getCommunity(id) {
  return request({
    url: '/community/community/' + id,
    method: 'get'
  })
}

// 新增小区信息
export function addCommunity(data) {
  return request({
    url: '/community/community/add',
    method: 'post',
    data: data
  })
}

// 修改小区信息
export function updateCommunity(data) {
  return request({
    url: '/community/community/edit',
    method: 'post',
    data: data
  })
}

// 删除小区信息
export function delCommunity(id) {
  return request({
    url: '/community/community/' + id,
    method: 'delete'
  })
}

