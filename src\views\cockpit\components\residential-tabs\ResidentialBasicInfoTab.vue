<template>
  <!-- 基本信息 Tab -->
  <div class="tab-pane-content">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在获取小区详细信息...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <el-icon class="error-icon"><Warning /></el-icon>
      <p>{{ error }}</p>
      <button @click="fetchCommunityDetail" class="retry-btn">重试</button>
    </div>

    <!-- 正常内容 -->
    <div v-else class="residential-info">
      <!-- 小区全景图片区域 -->
      <div class="residential-image-section">
        <div class="panorama-container">
          <PanoramaViewer :panorama-url="panoramaImageUrl" />
        </div>
        <div class="residential-name">{{ currentCommunityData.name || "暂无数据" }}</div>
      </div>

      <!-- 基本信息区域 -->
      <div class="residential-info-section">
        <div class="info-cards">
          <!-- 基本信息卡片 -->
          <div class="info-card">
            <div class="card-header">
              <el-icon class="card-icon"><OfficeBuilding /></el-icon>
              <span class="card-title">基本信息</span>
            </div>
            <div class="card-content">
              <div class="info-item">
                <span class="label">小区名称</span>
                <span class="value">{{ currentCommunityData.name || "暂无数据" }}</span>
              </div>
              <div class="info-item">
                <span class="label">小区组别</span>
                <span class="value">{{ currentCommunityData.communityGroupName || "暂无数据" }}</span>
              </div>
              <div class="info-item">
                <span class="label">总栋数</span>
                <span class="value">{{ formatNumber(currentCommunityData.totalBuildings) }}栋</span>
              </div>
            </div>
          </div>
          <!-- 楼栋性质卡片 -->
          <div class="info-card">
            <div class="card-header">
              <el-icon class="card-icon"><Location /></el-icon>
              <span class="card-title">楼栋性质</span>
            </div>
            <div class="card-content">
              <div class="info-item">
                <span class="label">是否成熟小区</span>
                <span class="value">{{ formatBoolean(currentCommunityData.isMature) }}</span>
              </div>
              <div class="info-item">
                <span class="label">是否公寓</span>
                <span class="value">{{ formatBoolean(currentCommunityData.hasApartment) }}</span>
              </div>
              <div class="info-item">
                <span class="label">是否住宅</span>
                <span class="value">{{ formatBoolean(currentCommunityData.hasDwelling) }}</span>
              </div>
            </div>
          </div>

          <!-- 面积信息卡片 -->
          <div class="info-card">
            <div class="card-header">
              <el-icon class="card-icon"><Crop /></el-icon>
              <span class="card-title">面积信息</span>
            </div>
            <div class="card-content">
              <div class="info-item">
                <span class="label">总占地面积</span>
                <span class="value">{{ formatArea(currentCommunityData.coverArea) }}</span>
              </div>
              <div class="info-item">
                <span class="label">总建筑面积</span>
                <span class="value">{{ formatArea(currentCommunityData.buildingArea) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, ref, computed, onMounted } from "vue";
import { getCommunity } from "@/api/cockpit";
import PanoramaViewer from "../PanoramaViewer.vue";
import { OfficeBuilding, Location, Crop, Warning } from "@element-plus/icons-vue";

const props = defineProps({
  residentialData: {
    type: Object,
    default: () => ({}),
  },
});

// 响应式数据
const loading = ref(false);
const communityDetail = ref({});
const error = ref("");

// 计算属性：优先使用API获取的详细数据，否则使用传入的基础数据
const currentCommunityData = computed(() => {
  return Object.keys(communityDetail.value).length > 0 ? communityDetail.value : props.residentialData;
});

// 全景图片URL - 优先使用小区数据中的全景图片，否则使用默认图片
const panoramaImageUrl = computed(() => {
  // 如果小区数据中有全景图片URL，使用它
  if (currentCommunityData.value.panoramaImage) {
    return currentCommunityData.value.panoramaImage;
  }
  // 否则使用默认的全景图片
  return new URL('/src/assets/cockpit/building.jpg', import.meta.url).href;
});



// 获取小区详细信息
const fetchCommunityDetail = async () => {
  if (!props.residentialData?.id) {
    console.warn("ResidentialBasicInfoTab: 缺少小区ID，无法获取详细信息");
    return;
  }

  try {
    loading.value = true;
    error.value = "";

    console.log("ResidentialBasicInfoTab: 开始获取小区详细信息，ID:", props.residentialData.id);
    const response = await getCommunity(props.residentialData.id);

    if (response.code === 200 && response.data) {
      communityDetail.value = response.data;
      console.log("ResidentialBasicInfoTab: 获取小区详细信息成功:", communityDetail.value);
    } else {
      error.value = response.msg || "获取小区详细信息失败";
      console.error("ResidentialBasicInfoTab: 获取小区详细信息失败:", response.msg);
    }
  } catch (err) {
    error.value = "网络请求异常，请稍后重试";
    console.error("ResidentialBasicInfoTab: 获取小区详细信息异常:", err);
  } finally {
    loading.value = false;
  }
};

// 格式化数字
const formatNumber = (num) => {
  if (!num || num === "暂无数据") return "暂无数据";
  return Number(num).toLocaleString();
};

// 格式化百分比
const formatPercentage = (rate) => {
  if (!rate && rate !== 0) return "暂无数据";
  return Number(rate).toFixed(1);
};

// 格式化是否字段
const formatBoolean = (value) => {
  if (value === 1 || value === "1") return "是";
  if (value === 0 || value === "0") return "否";
  return "暂无数据";
};

// 格式化面积
const formatArea = (area) => {
  if (!area) return "暂无数据";
  return `${Number(area).toLocaleString()}㎡`;
};

// 组件挂载时获取详细信息
onMounted(() => {
  fetchCommunityDetail();
});
</script>

<style scoped>
/* Tab内容区域 */
.tab-pane-content {
  padding: 20px;
  height: 100%;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: rgba(255, 255, 255, 0.8);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 254, 255, 0.3);
  border-top: 3px solid #00feff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.retry-btn {
  background: linear-gradient(135deg, rgba(0, 149, 255, 0.6) 0%, rgba(0, 149, 255, 0.8) 100%);
  border: 1px solid rgba(0, 149, 255, 0.3);
  color: #fff;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 15px;
}

.retry-btn:hover {
  background: linear-gradient(135deg, rgba(0, 149, 255, 0.8) 0%, rgba(0, 149, 255, 1) 100%);
  box-shadow: 0 0 8px rgba(0, 149, 255, 0.4);
}

/* 小区信息布局 */
.residential-info {
  display: flex;
  gap: 25px;
  height: 100%;
}

/* 小区图片区域 */
.residential-image-section {
  width: 450px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.panorama-container {
  width: 100%;
  height: 320px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 149, 255, 0.3);
  background: linear-gradient(135deg, rgba(20, 50, 120, 0.8) 0%, rgba(25, 55, 125, 0.6) 100%);
  position: relative;
}

.panorama-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(0, 254, 255, 0.1) 50%, transparent 70%);
  pointer-events: none;
  z-index: 1;
}

.residential-name {
  font-size: 20px;
  font-weight: 600;
  color: #00feff;
  text-align: center;
  padding: 12px;
  background: rgba(20, 50, 120, 0.6);
  border: 1px solid rgba(0, 149, 255, 0.3);
  border-radius: 8px;
  text-shadow: 0 0 8px rgba(0, 254, 255, 0.3);
}

/* 小区信息区域 */
.residential-info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-cards {
  display: flex;
  flex-direction: column;
  gap: 15px;
  height: 100%;
  overflow-y: auto;
}

.info-card {
  background: linear-gradient(135deg, rgba(20, 50, 120, 0.4) 0%, rgba(25, 55, 125, 0.6) 100%);
  border: 1px solid rgba(0, 149, 255, 0.2);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.info-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 254, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.info-card:hover::before {
  left: 100%;
}

.info-card:hover {
  border-color: rgba(0, 149, 255, 0.4);
  box-shadow: 0 0 20px rgba(0, 149, 255, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 149, 255, 0.2);
}

.card-icon {
  font-size: 16px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #00feff;
}

.card-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 15px;
}

.value {
  color: #00feff;
  font-weight: 500;
  font-size: 15px;
}

.value.occupancy {
  color: #ffa726;
  font-weight: 600;
  text-shadow: 0 0 5px rgba(255, 167, 38, 0.3);
}

/* 自定义滚动条 */
.info-cards::-webkit-scrollbar {
  width: 6px;
}

.info-cards::-webkit-scrollbar-track {
  background: rgba(20, 50, 120, 0.3);
  border-radius: 3px;
}

.info-cards::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #00feff, rgba(0, 149, 255, 0.8));
  border-radius: 3px;
  box-shadow: inset 0 0 3px rgba(0, 254, 255, 0.3);
}

.info-cards::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #00feff, #0095ff);
}
</style>
