<template>
  <!-- 租户信息 Tab -->
  <div class="tab-pane">
    <div class="table-section">
      <div class="section-header">
        <el-icon class="section-icon"><OfficeBuilding /></el-icon>
        <span class="section-title">入驻企业列表</span>
        <span class="tenant-count">共 {{ totalCount }} 家企业</span>
        
        <!-- 筛选器 -->
        <div class="filter-item">
          <label class="filter-label">层数筛选：</label>
          <select v-model="floorFilter" @change="handleFloorFilterChange" class="filter-select">
            <option value="">全部层数</option>
            <option v-for="floor in availableFloors" :key="floor" :value="floor">
              {{ floor }}层
            </option>
          </select>
        </div>
      </div>

      <div class="table-container">
        <el-table
          :data="paginatedTenants"
          v-loading="loading"
          element-loading-text="加载中..."
          element-loading-background="rgba(20, 50, 120, 0.8)"
          element-loading-spinner="el-icon-loading"
          class="tenant-table"
          height="100%"
          empty-text="暂无租户数据"
        >
          <el-table-column prop="name" label="企业名称" min-width="180" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="company-name">{{ row.name || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="uscCode" label="统一社会信用代码" min-width="160" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.uscCode || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <span class="status-badge" :class="getStatusClass(row.status)">
                {{ getStatusText(row.status) }}
              </span>
            </template>
          </el-table-column>

          <el-table-column prop="category" label="类别" min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.category || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="legalPerson" label="法人" min-width="100" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.legalPerson || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="concatMobile" label="联系电话" min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.concatMobile || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="address" label="地址" min-width="200" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.address || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="openDate" label="成立日期" width="120" align="center">
            <template #default="{ row }">
              <span>{{ row.openDate ? new Date(row.openDate).toLocaleDateString() : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="registeredCapital" label="注册资金" width="120" align="center">
            <template #default="{ row }">
              <span class="highlight">{{ row.registeredCapital ? (row.registeredCapital + '万元') : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="industryClassification" label="行业分类" min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="industry">{{ row.industryClassification || '-' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          :page-size="pagination.pageSize"
          :total="totalCount"
          :disabled="loading"
          layout="total, prev, pager, next"
          class="tenant-pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, computed, watch, onMounted } from 'vue';
import { listBuildingCompany } from '@/api/cockpit';
import { OfficeBuilding } from "@element-plus/icons-vue";

const props = defineProps({
  buildingData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits([]);

// 数据状态管理
const loading = ref(false);
const tenantsList = ref([]);
const totalCount = ref(0);

// 分页状态管理
const pagination = ref({
  currentPage: 1,
  pageSize: 5,
  total: 0
});

// 层数筛选
const floorFilter = ref('');

// 获取企业数据
const fetchTenantsList = async () => {
  if (!props.buildingData.id) return;

  try {
    loading.value = true;
    const params = {
      pageNum: pagination.value.currentPage,
      pageSize: pagination.value.pageSize,
      buildingId: props.buildingData.id
    };

    // 如果有层数筛选，添加到参数中
    if (floorFilter.value) {
      params.floor = floorFilter.value;
    }

    const response = await listBuildingCompany(params);

    if (response.code === 200) {
      tenantsList.value = response.rows || [];
      totalCount.value = response.total || 0;
      // 更新分页信息
      pagination.value.total = totalCount.value;
    } else {
      console.error('获取企业数据失败:', response.msg);
      tenantsList.value = [];
      totalCount.value = 0;
    }
  } catch (error) {
    console.error('获取企业数据异常:', error);
    tenantsList.value = [];
    totalCount.value = 0;
  } finally {
    loading.value = false;
  }
};

// 获取可用的层数列表
const availableFloors = computed(() => {
  const floors = [...new Set(tenantsList.value.map(tenant => tenant.floor).filter(floor => floor))];
  return floors.sort((a, b) => Number(a) - Number(b));
});

// 处理层数筛选变化
const handleFloorFilterChange = () => {
  pagination.value.currentPage = 1; // 重置到第一页
  fetchTenantsList(); // 重新获取数据
};

// 分页相关计算属性和方法
const paginatedTenants = computed(() => {
  return tenantsList.value;
});

// 状态样式和文本映射
const getStatusInfo = (status) => {
  const statusMap = {
    1: { text: '正常', class: 'status-active' },
    2: { text: '存续', class: 'status-active' },
    3: { text: '在业', class: 'status-active' },
    4: { text: '在营（开业）', class: 'status-active' },
    0: { text: '未知', class: 'status-unknown' }
  };
  return statusMap[status] || { text: '未知', class: 'status-unknown' };
};

const getStatusClass = (status) => {
  return getStatusInfo(status).class;
};

const getStatusText = (status) => {
  return getStatusInfo(status).text;
};

// Element Plus 分页处理方法
const handleCurrentChange = (page) => {
  pagination.value.currentPage = page;
  fetchTenantsList();
};

const handleSizeChange = (size) => {
  pagination.value.pageSize = size;
  pagination.value.currentPage = 1; // 重置到第一页
  fetchTenantsList();
};

// 监听楼栋数据变化
watch(() => props.buildingData.id, (newId) => {
  if (newId) {
    floorFilter.value = ''; // 重置筛选
    pagination.value.currentPage = 1; // 重置分页
    fetchTenantsList();
  }
}, { immediate: true });

// 组件挂载时获取数据
onMounted(() => {
  if (props.buildingData.id) {
    fetchTenantsList();
  }
});

// 暴露方法给父组件
defineExpose({
  resetFilter: () => {
    floorFilter.value = '';
    pagination.value.currentPage = 1;
    fetchTenantsList();
  }
});
</script>

<style scoped>
/* 表格相关样式 */
.table-section {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  padding: 12px 16px;
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.4) 0%,
    rgba(25, 55, 125, 0.6) 100%);
  border: 1px solid rgba(0, 149, 255, 0.2);
  border-radius: 6px;
}

.section-icon {
  font-size: 18px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #00feff;
}

.tenant-count {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(0, 149, 255, 0.2);
  padding: 4px 12px;
  border-radius: 12px;
  border: 1px solid rgba(0, 149, 255, 0.3);
}

/* 筛选器样式 */
.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.filter-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
}

.filter-select {
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.8) 0%,
    rgba(25, 55, 125, 0.9) 100%);
  border: 1px solid rgba(0, 149, 255, 0.3);
  border-radius: 4px;
  color: #fff;
  padding: 6px 12px;
  font-size: 14px;
  min-width: 120px;
  transition: all 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #00feff;
  box-shadow: 0 0 8px rgba(0, 254, 255, 0.3);
}

.filter-select option {
  background: rgba(20, 50, 120, 0.95);
  color: #fff;
  padding: 8px;
}

.table-container {
  flex: 1;
  overflow: hidden;
  border: 1px solid rgba(0, 149, 255, 0.2);
  border-radius: 8px;
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.3) 0%,
    rgba(25, 55, 125, 0.5) 100%);
}

/* Element Plus Table 样式覆盖 */
.tenant-table {
  background: transparent !important;
}

.tenant-table :deep(.el-table--border .el-table__inner-wrapper:after) {
  height: 0px !important;
}

.tenant-table :deep(.el-table__header-wrapper) {
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.8) 0%,
    rgba(25, 55, 125, 0.9) 100%) !important;
}

.tenant-table :deep(.el-table__header) {
  background: transparent !important;
}

.tenant-table :deep(.el-table__header th) {
  background: rgba(20, 50, 120, 1) !important;
  color: #00feff !important;
  font-weight: 600 !important;
  border-bottom: 2px solid rgba(0, 149, 255, 0.3) !important;
  border-right: 1px solid rgba(0, 149, 255, 0.2) !important;
  padding: 12px 8px !important;
}

.tenant-table :deep(.el-table__header th:last-child) {
  border-right: none !important;
}

.tenant-table :deep(.el-table__body-wrapper) {
  background: transparent !important;
}

.tenant-table :deep(.el-table__body) {
  background: transparent !important;
}

.tenant-table :deep(.el-table__row) {
  background: rgba(20, 50, 120, 0.2) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  transition: all 0.3s ease !important;
}

.tenant-table :deep(.el-table__row--striped) {
  background: rgba(25, 55, 125, 0.3) !important;
}

.tenant-table :deep(.el-table__row:hover) {
  background: rgba(2, 7, 11, 0.1) !important;
  transform: scale(1.01) !important;
}

.tenant-table :deep(.el-table__body tr:hover>td.el-table__cell){
  background: rgba(0, 149, 255, 0.1) !important;
  transform: scale(1.01) !important;
}


.tenant-table :deep(.el-table__row td) {
  border-bottom: 1px solid rgba(0, 149, 255, 0.1) !important;
  border-right: 1px solid rgba(0, 149, 255, 0.1) !important;
  padding: 10px 8px !important;
}

.tenant-table :deep(.el-table__row td:last-child) {
  border-right: none !important;
}

.tenant-table :deep(.el-table__border-left-patch) {
  background: transparent !important;
}

.tenant-table :deep(.el-table__border-right-patch) {
  background: transparent !important;
}

.tenant-table :deep(.el-table__empty-text) {
  color: rgba(255, 255, 255, 0.5) !important;
}

.tenant-table :deep(.el-loading-mask) {
  background-color: rgba(20, 50, 120, 0.8) !important;
}

.tenant-table :deep(.el-loading-text) {
  color: #00feff !important;
}

.tenant-table :deep(.el-table--fit .el-table__inner-wrapper:before) {
  width: 0px !important;
}


/* 表格内容样式 */
.company-name {
  color: #00feff !important;
  font-weight: 600 !important;
}

.highlight {
  color: #00ff88 !important;
  font-weight: 600 !important;
}

.industry {
  color: #ffa726 !important;
  font-weight: 500 !important;
}

.floor-number {
  color: #9c27b0 !important;
  font-weight: 600 !important;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-normal {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-abnormal {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.status-pending {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
  border: 1px solid rgba(255, 152, 0, 0.3);
}

.status-default {
  background: rgba(158, 158, 158, 0.2);
  color: #9e9e9e;
  border: 1px solid rgba(158, 158, 158, 0.3);
}

.status-active {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-unknown {
  background: rgba(158, 158, 158, 0.2);
  color: #9e9e9e;
  border: 1px solid rgba(158, 158, 158, 0.3);
}

/* 保留滚动条样式 */

/* Element Plus 分页样式覆盖 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  /* padding: 15px 16px; */
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.4) 0%,
    rgba(25, 55, 125, 0.6) 100%);
  border-radius: 6px;
  margin-top: 15px;
}

.tenant-pagination :deep(.el-pagination) {
  color: rgba(255, 255, 255, 0.8) !important;
}

.tenant-pagination :deep(.el-pagination__total) {
  color: rgba(255, 255, 255, 0.8) !important;
}

.tenant-pagination :deep(.el-pagination__sizes .el-select .el-input__inner) {
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.8) 0%,
    rgba(25, 55, 125, 0.9) 100%) !important;
  border: 1px solid rgba(0, 149, 255, 0.3) !important;
  color: #fff !important;
}

.tenant-pagination :deep(.el-pagination__sizes .el-select .el-input__inner:focus) {
  border-color: #00feff !important;
  box-shadow: 0 0 8px rgba(0, 254, 255, 0.3) !important;
}

.tenant-pagination :deep(.el-select-dropdown) {
  background: rgba(20, 50, 120, 0.95) !important;
  border: 1px solid rgba(0, 149, 255, 0.3) !important;
}

.tenant-pagination :deep(.el-select-dropdown__item) {
  color: #fff !important;
  background: transparent !important;
}

.tenant-pagination :deep(.el-select-dropdown__item:hover) {
  background: rgba(0, 149, 255, 0.2) !important;
}

.tenant-pagination :deep(.el-select-dropdown__item.selected) {
  background: rgba(0, 149, 255, 0.4) !important;
  color: #00feff !important;
}

.tenant-pagination :deep(.btn-prev),
.tenant-pagination :deep(.btn-next) {
  background: rgba(25, 55, 125, 0.6) !important;
  /* background: linear-gradient(135deg,
    rgba(0, 149, 255, 0.6) 0%,
    rgba(0, 149, 255, 0.8) 100%) !important; */
  border: 1px solid rgba(0, 149, 255, 0.3) !important;
  color: #fff !important;
}

.tenant-pagination :deep(.btn-prev:hover),
.tenant-pagination :deep(.btn-next:hover) {
  background: rgba(25, 55, 125, 0.6) !important;
  /* background: linear-gradient(135deg,
    rgba(0, 149, 255, 0.8) 0%,
    rgba(0, 149, 255, 1) 100%) !important; */
  box-shadow: 0 0 8px rgba(0, 149, 255, 0.4) !important;
}

.tenant-pagination :deep(.btn-prev:disabled),
.tenant-pagination :deep(.btn-next:disabled) {
  background: rgba(25, 55, 125, 0.4) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.3) !important;
}

.tenant-pagination :deep(.el-pager li) {
  background: rgba(25, 55, 125, 0.6) !important;
  border: 1px solid rgba(0, 149, 255, 0.2) !important;
  color: rgba(255, 255, 255, 0.8) !important;
  margin: 0 2px !important;
}

.tenant-pagination :deep(.el-pager li:hover) {
  background: rgba(0, 149, 255, 0.3) !important;
  border-color: rgba(0, 149, 255, 0.4) !important;
  color: white !important;
}

.tenant-pagination :deep(.el-pager li.is-active) {
  background: linear-gradient(135deg,
    rgba(0, 149, 255, 0.8) 0%,
    rgba(0, 254, 255, 0.6) 100%) !important;
  border-color: rgba(0, 149, 255, 0.6) !important;
  color: white !important;
  font-weight: 600 !important;
  box-shadow: 0 0 8px rgba(0, 149, 255, 0.4) !important;
}

.tenant-pagination :deep(.el-pagination__jump) {
  color: rgba(255, 255, 255, 0.8) !important;
}

.tenant-pagination :deep(.el-pagination__editor .el-input__inner) {
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.8) 0%,
    rgba(25, 55, 125, 0.9) 100%) !important;
  border: 1px solid rgba(0, 149, 255, 0.3) !important;
  color: #fff !important;
}

.tenant-pagination :deep(.el-pagination__editor .el-input__inner:focus) {
  border-color: #00feff !important;
  box-shadow: 0 0 8px rgba(0, 254, 255, 0.3) !important;
}

/* 滚动条样式 */
.tenant-table :deep(.el-table__body-wrapper)::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.tenant-table :deep(.el-table__body-wrapper)::-webkit-scrollbar-track {
  background: rgba(20, 50, 120, 0.3);
  border-radius: 4px;
}

.tenant-table :deep(.el-table__body-wrapper)::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #00feff, rgba(0, 149, 255, 0.8));
  border-radius: 4px;
  box-shadow: inset 0 0 3px rgba(0, 254, 255, 0.3);
}

.tenant-table :deep(.el-table__body-wrapper)::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #00feff, #0095ff);
}
</style>
