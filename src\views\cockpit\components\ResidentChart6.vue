<template>
  <div
    class="w-[618px] h-[314px] p-[20px] mt-1 relative bg-no-repeat bg-contain bg-[url(@/assets/cockpit/bg/border.png)]">
    <div class="w-[200px] h-[30px] leading-[30px] text-white text-lg mb-3 pl-2 flex items-center">
      <span class="w-[4px] h-[18px] bg-[#00feff] mr-2 inline-block"></span>
      公寓楼栋入住率
    </div>
    <div ref="chartRef" class="w-full h-[231px]"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts";

// 图表引用
const chartRef = ref(null);
let chartInstance = null;

onMounted(() => {
  initChart();
  // 添加窗口大小变化的监听，自动调整图表大小
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  // 清除图表实例
  chartInstance && chartInstance.dispose();
  // 移除窗口大小变化的监听
  window.removeEventListener('resize', handleResize);
});

// 处理窗口大小变化，调整图表大小
const handleResize = () => {
  chartInstance && chartInstance.resize();
};

// 初始化图表
const initChart = () => {
  // 检查DOM元素是否存在
  if (!chartRef.value) return;

  // 如果图表实例已存在，则销毁
  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartRef.value, "macarons");

  // 从图片中提取的公寓数据
  const apartments = [
    { name: '万科京都荟', rate: 45.03, area: '中区' },
    { name: '保利东坝花园', rate: 39.76, area: '东区' },
    { name: '依云国际', rate: 39.58, area: '西区' },
    { name: '新霞广场', rate: 30.18, area: '中区' },
    { name: '怡龙湾', rate: 16.05, area: '南区' },
    { name: '中德港', rate: 4.55, area: '南区' },
    { name: '国金中心', rate: 2.73, area: '中区' }
  ];

  // 年份作为X轴
  const years = ['2018年', '2019年', '2020年', '2021年', '2022年', '2023年'];

  // 为每个公寓生成模拟的历史数据（基于当前入住率，逐年递减）
  const generateHistoricalData = (currentRate) => {
    // 模拟历史数据，假设每年的入住率递减，最早年份约为当前的30%-50%
    const rates = [];
    for (let i = 5; i >= 0; i--) {
      const factor = 0.5 + (i * 0.1); // 从0.5到1.0的系数
      rates.push(Math.round((currentRate * factor) * 100) / 100);
    }
    return rates;
  };

  // 生成每个公寓的历史数据
  const apartmentData = apartments.map(apartment => {
    return {
      name: apartment.name,
      area: apartment.area,
      data: generateHistoricalData(apartment.rate)
    };
  });

  // 蓝色系渐变色系列
  const colors = [
    // 万科京都荟
    new echarts.graphic.LinearGradient(0, 0, 1, 0, [
      { offset: 0, color: '#60acfc' },
      { offset: 1, color: '#4096ff' }
    ]),
    // 保利东坝花园
    new echarts.graphic.LinearGradient(0, 0, 1, 0, [
      { offset: 0, color: '#32d3eb' },
      { offset: 1, color: '#0ac2e9' }
    ]),
    // 依云国际
    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
      { offset: 0, color: '#5ad8a6' },
      { offset: 1, color: '#36cfc9' }
    ]),
    // 新霞广场
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#9fe6b8' },
      { offset: 1, color: '#67d39e' }
    ]),
    // 怡龙湾
    new echarts.graphic.LinearGradient(0, 0, 1, 0, [
      { offset: 0, color: '#fad8be' },
      { offset: 1, color: '#f6bd16' }
    ]),
    // 中德港
    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
      { offset: 0, color: '#82f4d7' },
      { offset: 1, color: '#3dbb9f' }
    ]),
    // 国金中心
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#56d0e3' },
      { offset: 1, color: '#3aa1c0' }
    ])
  ];

  // 构建系列数据，每个公寓一个系列
  const series = apartmentData.map((apartment, index) => {
    return {
      name: apartment.name,
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 5,
      data: apartment.data,
      lineStyle: {
        width: 2
      }
    };
  });

  // 创建折线图配置
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: function (params) {
        let result = `${params[0].axisValue}<br/>`;

        // 按入住率从高到低排序
        params.sort((a, b) => b.value - a.value);

        params.forEach(param => {
          const color = param.color;
          const marker = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
          const apartmentInfo = apartmentData.find(a => a.name === param.seriesName);
          const area = apartmentInfo ? ` (${apartmentInfo.area})` : '';
          result += `${marker}${param.seriesName}${area}: ${param.value}%<br/>`;
        });

        return result;
      },
      backgroundColor: 'rgba(10, 30, 50, 0.7)',
      borderColor: 'rgba(48, 207, 208, 0.3)',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 8,
      top: 10,
      bottom: 20,
      data: apartments.map(a => a.name),
      textStyle: {
        color: '#fff',
        fontSize: 12,
        lineHeight: 13
      },
      padding: [10, 0, 0, 0],
      pageButtonItemGap: 5,
      pageButtonGap: 5,
      pageButtonPosition: 'end',
      pageFormatter: '{current}/{total}',
      pageIconColor: '#aaa',
      pageIconInactiveColor: '#2f4554',
      pageIconSize: 12,
      pageTextStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: '3%',
      right: '30%',
      bottom: '3%',
      top: '18%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: years,
        axisPointer: {
          type: 'shadow'
        },
        axisTick: {
          alignWithLabel: true,
          show: false
        },
        axisLine: {
          lineStyle: {
            color: '#1a5cd7'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 12
        }
      }
    ],
    yAxis: {
      type: 'value',
      name: '入住率(%)',
      min: 0,
      max: 100,
      interval: 20,
      nameTextStyle: {
        color: '#fff',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: '#1a5cd7'
        }
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12,
        formatter: '{value}%'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(26, 92, 215, 0.3)',
          type: 'dashed'
        }
      }
    },
    // 蓝色系渐变色系列
    color: colors,
    series: series
  };

  chartInstance.setOption(option);
};
</script>

<style scoped>
/* 可以添加组件特有的样式 */
</style> 