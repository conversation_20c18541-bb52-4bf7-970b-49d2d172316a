import request from '@/utils/request'
// 查询常住人口数据列表
export function listPropertyData(query) {
  return request({
    url: '/community/property_data/list',
    method: 'get',
    params: query
  })
}

// 查询常住人口数据详细
export function getPropertyData(id) {
  return request({
    url: '/community/property_data/' + id,
    method: 'get'
  })
}

// 新增常住人口数据
export function addPropertyData(data) {
  return request({
    url: '/community/property_data/add',
    method: 'post',
    data: data
  })
}

// 修改常住人口数据
export function updatePropertyData(data) {
  return request({
    url: '/community/property_data/edit',
    method: 'post',
    data: data
  })
}

// 删除常住人口数据
export function delPropertyData(id) {
  return request({
    url: '/community/property_data/' + id,
    method: 'delete'
  })
}

