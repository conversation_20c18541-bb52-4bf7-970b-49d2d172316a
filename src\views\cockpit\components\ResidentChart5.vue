<template>
  <div
    class="w-[618px] h-[314px] p-[20px] mt-1 relative bg-no-repeat bg-contain bg-[url(@/assets/cockpit/bg/border.png)]">
    <div class="w-[200px] h-[30px] leading-[30px] text-white text-lg mb-3 pl-2 flex items-center">
      <span class="w-[4px] h-[18px] bg-[#00feff] mr-2 inline-block"></span>
      公寓居住分布
    </div>
    <div ref="chartRef" class="w-full h-[210px]"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts";
import { generateUniqueColors } from "@/utils/cockpit";
import { getApartmentDistribution } from "@/api/cockpit/index";

// 图表引用
const chartRef = ref(null);
let chartInstance = null;

// 图表数据 - 参考 Chart2 格式
const chartData = ref({
  title: '公寓居住分布',
  data: []
});

onMounted(() => {
  fetchData();
  // 添加窗口大小变化的监听，自动调整图表大小
  window.addEventListener('resize', handleResize);
});

// 获取数据
const fetchData = async () => {
  try {
    const response = await getApartmentDistribution();
    if (response.code === 200) {
      chartData.value = response.data;
      initChart();
    } else {
      console.error('获取公寓居住分布数据失败:', response.msg);
    }
  } catch (error) {
    console.error('获取公寓居住分布数据异常:', error);
  }
};

onUnmounted(() => {
  // 清除图表实例
  chartInstance && chartInstance.dispose();
  // 移除窗口大小变化的监听
  window.removeEventListener('resize', handleResize);
});

// 处理窗口大小变化，调整图表大小
const handleResize = () => {
  chartInstance && chartInstance.resize();
};

// 初始化图表
const initChart = () => {
  // 检查DOM元素是否存在
  if (!chartRef.value) return;

  // 如果图表实例已存在，则销毁
  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartRef.value, "macarons");

  // 使用动态数据 - 参考 Chart2 格式
  const pieData = chartData.value.data;

  // 使用公共函数生成独特颜色
  const colors = generateUniqueColors(chartData.value.data.length);

  const option = {
    backgroundColor: 'transparent',
    // 设置全局颜色系列，确保图例显示不同颜色
    color: colors,
    tooltip: {
      trigger: 'item',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      type: "scroll",
      orient: "vertical",
      right: 8,
      top: 10,
      bottom: 20,
      width: 120, // 固定图例宽度
      data: pieData.map(item => item.name),
      textStyle: {
        color: "#fff",
        fontSize: 12,
        lineHeight: 13,
        width: 100, // 固定文字宽度
        overflow: "truncate", // 溢出截断
        ellipsis: "...", // 显示省略号
      },
      padding: [10, 0, 0, 0],
      // 自定义格式化函数，确保文字长度控制
      formatter: function (name) {
        if (name.length > 8) {
          return name.substring(0, 8) + "...";
        }
        return name;
      },
    },
    series: [
      {
        name: '公寓居住',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['30%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: 'rgba(10, 20, 30, 0.2)',
          borderWidth: 2
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: pieData,
      }
    ]
  };

  chartInstance.setOption(option);
};
</script>

<style scoped>
/* 可以添加组件特有的样式 */
</style>