<template>
  <!-- 租控图 Tab -->
  <div class="tab-pane">
    <div class="rental-control-section">
      <div class="section-header">
        <el-icon class="section-icon"><OfficeBuilding /></el-icon>
        <span class="section-title">楼宇租控图</span>
        <span class="tenant-count">共 {{ totalUnitsCount }} 个单元</span>
      </div>

      <!-- 三栏布局：左侧楼层、中间单元、右侧图例 -->
      <div class="rental-main-container">
        <!-- 左侧楼层和中间单元的联合滚动区域 -->
        <div class="floors-units-container">
          <!-- 固定头部 -->
          <div class="floors-units-header">
            <div class="floors-header-cell">楼层</div>
            <div class="units-header-cell">单元分布</div>
          </div>

          <!-- 可滚动内容区域 -->
          <div class="floors-units-content">
            <div
              v-for="(floor, index) in floorData"
              :key="floor.number"
              class="floor-row"
              :class="{ 'has-separator': index < floorData.length - 1 }"
            >
              <!-- 楼层标签 -->
              <div class="floor-label-cell">
                <span class="floor-number">{{ floor.number }}F</span>
              </div>

              <!-- 单元格区域 -->
              <div class="floor-units-cell">
                <div class="floor-units">
                  <div
                    v-for="(unit, unitIndex) in floor.units"
                    :key="unitIndex"
                    class="rental-unit"
                    :class="{ hidden: !isUnitVisible(unit) }"
                    :style="{ backgroundColor: getUnitDisplayColor(unit) }"
                    :title="unit.tooltip"
                  >
                    <span class="unit-number">{{ unit.unitNumber }}</span>
                    <span class="unit-info" v-if="unit.info">{{ unit.info }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧图例区域 -->
        <div class="legend-sidebar">
          <div class="legend-header">
            <div class="legend-title">图例统计</div>
            <div class="legend-summary">
              总单元: {{ totalUnitsCount }}<br />
              已出租: {{ getLegendCount("已出租") }}<br />
              入住率: {{ getOccupancyRate() }}%
            </div>
          </div>
          <div class="legend-items">
            <div
              v-for="status in rentalStatuses"
              :key="status.name"
              class="legend-item clickable"
              :class="{ disabled: !legendVisibility[status.name] }"
              @click="toggleLegendVisibility(status.name)"
            >
              <div class="legend-color" :style="{ background: legendVisibility[status.name] ? status.color : '#666' }"></div>
              <span>{{ status.name }}</span>
              <span class="legend-count">({{ getLegendCount(status.name) }})</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, ref, computed } from "vue";
import { OfficeBuilding } from "@element-plus/icons-vue";

const props = defineProps({
  buildingData: {
    type: Object,
    default: () => ({}),
  },
});

// 图例显示控制
const legendVisibility = ref({});

// 定义租控状态类型和颜色
const rentalStatuses = [
  { name: "已出租", color: "#FFD700" }, // 黄色
  { name: "功能区", color: "#FF6B6B" }, // 红色
  { name: "在淡客户", color: "#4ECDC4" }, // 青色
  { name: "不存在", color: "#95A5A6" }, // 灰色
  { name: "空置", color: "#00FEFF" }, // 蓝色
  { name: "其他", color: "#9B59B6" }, // 紫色
];

// 初始化图例显示状态
const initializeLegendVisibility = () => {
  if (Object.keys(legendVisibility.value).length === 0) {
    rentalStatuses.forEach((status) => {
      legendVisibility.value[status.name] = true;
    });
  }
};

// 获取状态颜色
const getStatusColor = (statusName) => {
  const status = rentalStatuses.find((s) => s.name === statusName);
  return status ? status.color : "#666";
};

// 生成楼层数据
const floorData = computed(() => {
  initializeLegendVisibility();

  // 模拟楼层数据，每层包含不同数量和状态的单元
  const floors = [];
  const statuses = ["已出租", "功能区", "在淡客户", "不存在", "空置", "其他"];

  // 生成30层楼的数据，从高层到低层排序（30F -> 1F）
  for (let floor = 30; floor >= 1; floor--) {
    const unitsPerFloor = Math.floor(Math.random() * 8) + 4; // 每层4-12个单元
    const units = [];

    for (let unit = 1; unit <= unitsPerFloor; unit++) {
      const unitNumber = `${floor}${String(unit).padStart(2, "0")}`;
      const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];

      // 根据状态生成不同的信息
      let info = "";
      if (randomStatus === "已出租") {
        const companies = [
          "腾讯科技",
          "阿里巴巴",
          "百度网络",
          "字节跳动",
          "美团",
          "滴滴出行",
          "小米科技",
          "华为技术",
          "京东集团",
          "网易科技",
        ];
        info = companies[Math.floor(Math.random() * companies.length)];
      } else if (randomStatus === "功能区") {
        const areas = ["会议室", "休息区", "茶水间", "打印区", "接待区", "健身房", "餐厅"];
        info = areas[Math.floor(Math.random() * areas.length)];
      } else if (randomStatus === "在淡客户") {
        const prospects = ["意向客户A", "意向客户B", "洽谈中", "考察中", "预约看房"];
        info = prospects[Math.floor(Math.random() * prospects.length)];
      }

      units.push({
        unitNumber,
        info,
        status: randomStatus,
        color: getStatusColor(randomStatus),
        tooltip: `${floor}F-${unitNumber} - ${info || randomStatus}`,
      });
    }

    floors.push({
      number: floor,
      units,
    });
  }

  return floors;
});

// 计算总单元数
const totalUnitsCount = computed(() => {
  return floorData.value.reduce((total, floor) => total + floor.units.length, 0);
});

// 切换图例显示状态
const toggleLegendVisibility = (name) => {
  legendVisibility.value[name] = !legendVisibility.value[name];
};

// 判断单元是否可见
const isUnitVisible = (unit) => {
  return legendVisibility.value[unit.status] !== false;
};

// 获取单元显示颜色
const getUnitDisplayColor = (unit) => {
  if (legendVisibility.value[unit.status] === false) {
    return "#666"; // 灰色表示隐藏
  }
  return unit.color;
};

// 获取图例统计数据
const getLegendCount = (statusName) => {
  let count = 0;
  floorData.value.forEach((floor) => {
    floor.units.forEach((unit) => {
      if (unit.status === statusName) {
        count++;
      }
    });
  });
  return count;
};

// 获取入住率
const getOccupancyRate = () => {
  const total = totalUnitsCount.value;
  if (total === 0) return 0;
  const occupied = getLegendCount("已出租");
  return Math.round((occupied / total) * 100);
};

// 暴露方法给父组件
defineExpose({
  resetFilter: () => {
    // 重置图例显示状态
    Object.keys(legendVisibility.value).forEach((key) => {
      legendVisibility.value[key] = true;
    });
  },
});
</script>

<style scoped>
/* 租控图样式 */
.rental-control-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(20, 50, 120, 0.4) 0%, rgba(25, 55, 125, 0.6) 100%);
  border: 1px solid rgba(0, 149, 255, 0.2);
  border-radius: 6px;
}

.section-icon {
  font-size: 18px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #00feff;
}

.tenant-count {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(0, 149, 255, 0.2);
  padding: 4px 12px;
  border-radius: 12px;
  border: 1px solid rgba(0, 149, 255, 0.3);
}

/* 三栏布局容器 */
.rental-main-container {
  flex: 1;
  display: flex;
  gap: 10px;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(20, 50, 120, 0.3) 0%, rgba(25, 55, 125, 0.5) 100%);
  border: 1px solid rgba(0, 149, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
}

/* 楼层和单元联合区域 */
.floors-units-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, rgba(20, 50, 120, 0.2) 0%, rgba(25, 55, 125, 0.3) 100%);
  border: 1px solid rgba(0, 149, 255, 0.2);
  border-radius: 6px;
  overflow: hidden;
}

/* 固定头部 */
.floors-units-header {
  display: flex;
  background: linear-gradient(135deg, rgba(20, 50, 120, 0.6) 0%, rgba(25, 55, 125, 0.8) 100%);
  border-bottom: 1px solid rgba(0, 149, 255, 0.2);
}

.floors-header-cell {
  width: 80px;
  padding: 10px;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #00feff;
  border-right: 1px solid rgba(0, 149, 255, 0.2);
}

.units-header-cell {
  flex: 1;
  padding: 10px;
  font-size: 14px;
  font-weight: 600;
  color: #00feff;
}

/* 可滚动内容区域 */
.floors-units-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

.floor-row {
  display: flex;
  margin-bottom: 8px;
  padding-bottom: 8px;
}

.floor-row.has-separator {
  border-bottom: 1px solid rgba(0, 149, 255, 0.1);
  margin-bottom: 12px;
  padding-bottom: 12px;
}

/* 楼层标签单元格 */
.floor-label-cell {
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-right: 1px solid rgba(0, 149, 255, 0.1);
}

.floor-number {
  font-weight: 600;
  color: #00feff;
  font-size: 12px;
}

/* 单元格区域 */
.floor-units-cell {
  flex: 1;
  padding: 0 10px;
}

.floor-units {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  min-height: 40px;
  align-items: center;
}

/* 右侧图例区域 */
.legend-sidebar {
  width: 200px;
  background: linear-gradient(135deg, rgba(20, 50, 120, 0.4) 0%, rgba(25, 55, 125, 0.6) 100%);
  border: 1px solid rgba(0, 149, 255, 0.2);
  border-radius: 6px;
  padding: 15px;
  overflow-y: auto;
}

.legend-header {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 149, 255, 0.2);
}

.legend-title {
  font-size: 16px;
  color: #00feff;
  font-weight: 600;
  margin-bottom: 8px;
}

.legend-summary {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  padding: 6px 8px;
  border-radius: 4px;
  border: 1px solid transparent;
}

.legend-item.clickable {
  cursor: pointer;
}

.legend-item.clickable:hover {
  background: rgba(0, 149, 255, 0.1);
  border-color: rgba(0, 149, 255, 0.3);
}

.legend-item.disabled {
  opacity: 0.5;
}

.legend-item.disabled span {
  text-decoration: line-through;
}

.legend-color {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

.legend-count {
  margin-left: auto;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

/* 单元格样式 */
.rental-unit {
  min-width: 60px;
  max-width: 120px;
  height: 35px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2px 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.rental-unit:hover {
  transform: scale(1.02);
  border-color: #00feff;
  box-shadow: 0 0 8px rgba(0, 254, 255, 0.3);
}

.rental-unit.hidden {
  opacity: 0.3;
  filter: grayscale(100%);
}

.unit-number {
  font-size: 10px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.8);
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.8);
  line-height: 1;
}

.unit-info {
  font-size: 8px;
  color: rgba(0, 0, 0, 0.8);
  text-align: center;
  line-height: 1.1;
  margin-top: 1px;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.8);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

/* 滚动条样式 */
.floors-units-content::-webkit-scrollbar,
.legend-sidebar::-webkit-scrollbar {
  width: 6px;
}

.floors-units-content::-webkit-scrollbar-track,
.legend-sidebar::-webkit-scrollbar-track {
  background: rgba(20, 50, 120, 0.3);
  border-radius: 3px;
}

.floors-units-content::-webkit-scrollbar-thumb,
.legend-sidebar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #00feff, rgba(0, 149, 255, 0.8));
  border-radius: 3px;
  box-shadow: inset 0 0 3px rgba(0, 254, 255, 0.3);
}

.floors-units-content::-webkit-scrollbar-thumb:hover,
.legend-sidebar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #00feff, #0095ff);
}
</style>
