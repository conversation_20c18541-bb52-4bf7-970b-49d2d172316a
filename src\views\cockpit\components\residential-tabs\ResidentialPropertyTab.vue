<template>
  <!-- 物业信息 Tab -->
  <div class="tab-pane">
    <div class="table-section">
      <div class="section-header">
        <el-icon class="section-icon"><House /></el-icon>
        <span class="section-title">物业管理信息</span>
        <span class="property-count">共 {{ totalCount }} 条记录</span>
        
        <!-- 筛选器 -->
        <div class="filter-item">
          <label class="filter-label">楼栋筛选：</label>
          <select v-model="buildingFilter" @change="handleBuildingFilterChange" class="filter-select">
            <option value="">全部楼栋</option>
            <option v-for="building in availableBuildings" :key="building" :value="building">
              {{ building }}栋
            </option>
          </select>
        </div>
      </div>

      <div class="table-container">
        <el-table
          :data="paginatedProperties"
          v-loading="loading"
          element-loading-text="加载中..."
          element-loading-background="rgba(0, 29, 88, 0.8)"
          element-loading-spinner="el-icon-loading"
          class="property-table"
          height="100%"
          empty-text="暂无物业数据"
        >
          <el-table-column prop="unitNumber" label="房屋编号" width="120" align="center">
            <template #default="{ row }">
              <span class="unit-number">{{ row.unitNumber || '-' }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="buildingNumber" label="楼栋" width="80" align="center">
            <template #default="{ row }">
              <span class="building-number">{{ row.buildingNumber || '-' }}栋</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="floor" label="楼层" width="80" align="center">
            <template #default="{ row }">
              <span>{{ row.floor || '-' }}层</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="roomNumber" label="房间号" width="100" align="center">
            <template #default="{ row }">
              <span>{{ row.roomNumber || '-' }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="area" label="建筑面积" width="120" align="center">
            <template #default="{ row }">
              <span>{{ row.area ? row.area + '㎡' : '-' }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="propertyType" label="房屋类型" width="100" align="center">
            <template #default="{ row }">
              <span>{{ row.propertyType || '-' }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="入住状态" width="100" align="center">
            <template #default="{ row }">
              <span class="status-badge" :class="getStatusClass(row.status)">
                {{ getStatusText(row.status) }}
              </span>
            </template>
          </el-table-column>
          
          <el-table-column prop="ownerName" label="业主姓名" min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.ownerName || '-' }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="ownerPhone" label="联系电话" min-width="130" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.ownerPhone || '-' }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="moveInDate" label="入住时间" width="120" align="center">
            <template #default="{ row }">
              <span>{{ row.moveInDate ? new Date(row.moveInDate).toLocaleDateString() : '-' }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="propertyFee" label="物业费" width="100" align="center">
            <template #default="{ row }">
              <span class="highlight">{{ row.propertyFee ? row.propertyFee + '元/月' : '-' }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="paymentStatus" label="缴费状态" width="120" align="center">
            <template #default="{ row }">
              <span class="payment-badge" :class="getPaymentClass(row.paymentStatus)">
                {{ getPaymentText(row.paymentStatus) }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[5, 10, 20, 50]"
          :total="totalCount"
          :disabled="loading"
          layout="total, prev, pager, next"
          class="property-pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, ref, computed, watch, onMounted } from 'vue';
import { House } from "@element-plus/icons-vue";

const props = defineProps({
  residentialData: {
    type: Object,
    default: () => ({})
  }
});

// 数据状态管理
const loading = ref(false);
const propertiesList = ref([]);
const totalCount = ref(0);

// 分页状态管理
const pagination = ref({
  currentPage: 1,
  pageSize: 5,
  total: 0
});

// 楼栋筛选
const buildingFilter = ref('');

// 模拟物业数据
const generateMockPropertyData = () => {
  const mockData = [];
  const propertyTypes = ['住宅', '商铺', '车位', '储藏室'];
  const statuses = [1, 2, 3]; // 1-已入住, 2-空置, 3-装修中
  const paymentStatuses = [1, 2, 3]; // 1-已缴费, 2-欠费, 3-部分缴费
  
  for (let i = 1; i <= 156; i++) {
    const buildingNum = Math.ceil(i / 20);
    const floor = Math.ceil((i % 20 || 20) / 4);
    const roomNum = ((i - 1) % 4) + 1;
    
    mockData.push({
      id: i,
      unitNumber: `${String(buildingNum).padStart(2, '0')}-${String(floor).padStart(2, '0')}-${String(roomNum).padStart(2, '0')}`,
      buildingNumber: buildingNum,
      floor: floor,
      roomNumber: `${floor}${String(roomNum).padStart(2, '0')}`,
      area: Math.floor(Math.random() * 50) + 80, // 80-130㎡
      propertyType: propertyTypes[Math.floor(Math.random() * propertyTypes.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      ownerName: `业主${i}`,
      ownerPhone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
      moveInDate: new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
      propertyFee: Math.floor(Math.random() * 200) + 150, // 150-350元/月
      paymentStatus: paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)]
    });
  }
  
  return mockData;
};

// 获取物业数据
const fetchPropertiesList = async () => {
  try {
    loading.value = true;
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 使用模拟数据
    const allData = generateMockPropertyData();
    
    // 应用楼栋筛选
    let filteredData = allData;
    if (buildingFilter.value) {
      filteredData = allData.filter(item => item.buildingNumber === parseInt(buildingFilter.value));
    }
    
    // 分页处理
    const start = (pagination.value.currentPage - 1) * pagination.value.pageSize;
    const end = start + pagination.value.pageSize;
    
    propertiesList.value = filteredData.slice(start, end);
    totalCount.value = filteredData.length;
    pagination.value.total = totalCount.value;
    
    console.log('获取物业数据成功:', propertiesList.value);
  } catch (error) {
    console.error('获取物业数据异常:', error);
    propertiesList.value = [];
    totalCount.value = 0;
  } finally {
    loading.value = false;
  }
};

// 获取可用的楼栋列表
const availableBuildings = computed(() => {
  const buildings = [...new Set(generateMockPropertyData().map(item => item.buildingNumber))];
  return buildings.sort((a, b) => a - b);
});

// 处理楼栋筛选变化
const handleBuildingFilterChange = () => {
  pagination.value.currentPage = 1; // 重置到第一页
  fetchPropertiesList(); // 重新获取数据
};

// 分页相关计算属性和方法
const paginatedProperties = computed(() => {
  return propertiesList.value;
});

// Element Plus 分页处理方法
const handleCurrentChange = (page) => {
  pagination.value.currentPage = page;
  fetchPropertiesList();
};

const handleSizeChange = (size) => {
  pagination.value.pageSize = size;
  pagination.value.currentPage = 1; // 重置到第一页
  fetchPropertiesList();
};

// 状态样式和文本映射
const getStatusInfo = (status) => {
  const statusMap = {
    1: { text: '已入住', class: 'status-occupied' },
    2: { text: '空置', class: 'status-vacant' },
    3: { text: '装修中', class: 'status-renovating' }
  };
  return statusMap[status] || { text: '未知', class: 'status-unknown' };
};

const getStatusClass = (status) => {
  return getStatusInfo(status).class;
};

const getStatusText = (status) => {
  return getStatusInfo(status).text;
};

// 缴费状态样式和文本映射
const getPaymentInfo = (status) => {
  const statusMap = {
    1: { text: '已缴费', class: 'payment-paid' },
    2: { text: '欠费', class: 'payment-overdue' },
    3: { text: '部分缴费', class: 'payment-partial' }
  };
  return statusMap[status] || { text: '未知', class: 'payment-unknown' };
};

const getPaymentClass = (status) => {
  return getPaymentInfo(status).class;
};

const getPaymentText = (status) => {
  return getPaymentInfo(status).text;
};

// 监听小区数据变化
watch(() => props.residentialData.id, (newId) => {
  if (newId) {
    buildingFilter.value = ''; // 重置筛选
    pagination.value.currentPage = 1; // 重置分页
    fetchPropertiesList();
  }
}, { immediate: true });

// 组件挂载时获取数据
onMounted(() => {
  if (props.residentialData.id) {
    fetchPropertiesList();
  }
});

// 暴露方法给父组件
defineExpose({
  resetFilter: () => {
    buildingFilter.value = '';
    pagination.value.currentPage = 1;
    fetchPropertiesList();
  }
});
</script>

<style scoped>
/* Tab内容区域 */
.tab-pane {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow: hidden;
}

/* 表格头部 */
.section-header {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.6) 0%,
    rgba(25, 55, 125, 0.8) 100%);
  border: 1px solid rgba(0, 149, 255, 0.3);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.section-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(0, 254, 255, 0.1),
    transparent);
  transition: left 0.6s ease;
}

.section-header:hover::before {
  left: 100%;
}

.section-icon {
  font-size: 20px;
  color: #00feff;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #00feff;
  text-shadow: 0 0 8px rgba(0, 254, 255, 0.3);
}

.property-count {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin-left: auto;
}

/* 筛选器样式 */
.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  white-space: nowrap;
}

.filter-select {
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.8) 0%,
    rgba(25, 55, 125, 0.9) 100%);
  border: 1px solid rgba(0, 149, 255, 0.3);
  color: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: all 0.3s ease;
}

.filter-select:focus {
  border-color: #00feff;
  box-shadow: 0 0 8px rgba(0, 254, 255, 0.3);
}

.filter-select option {
  background: rgba(20, 50, 120, 0.95);
  color: #fff;
}

/* 表格容器 */
.table-container {
  flex: 1;
  overflow: hidden;
  border: 1px solid rgba(0, 149, 255, 0.2);
  border-radius: 8px;
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.3) 0%,
    rgba(25, 55, 125, 0.5) 100%);
}

/* Element Plus Table 样式覆盖 */

/* Element Plus Table 样式覆盖 */
.property-table {
  background: transparent !important;
}

.property-table :deep(.el-table--border .el-table__inner-wrapper:after) {
  height: 0px !important;
}

.property-table :deep(.el-table__header-wrapper) {
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.8) 0%,
    rgba(25, 55, 125, 0.9) 100%) !important;
}

.property-table :deep(.el-table__header) {
  background: transparent !important;
}

.property-table :deep(.el-table__header th) {
  background: rgba(20, 50, 120, 1) !important;
  color: #00feff !important;
  font-weight: 600 !important;
  border-bottom: 2px solid rgba(0, 149, 255, 0.3) !important;
  border-right: 1px solid rgba(0, 149, 255, 0.2) !important;
  padding: 12px 8px !important;
}

.property-table :deep(.el-table__header th:last-child) {
  border-right: none !important;
}

.property-table :deep(.el-table__body-wrapper) {
  background: transparent !important;
}

.property-table :deep(.el-table__body) {
  background: transparent !important;
}

.property-table :deep(.el-table__row) {
  background: rgba(20, 50, 120, 0.2) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  transition: all 0.3s ease !important;
}

.property-table :deep(.el-table__row--striped) {
  background: rgba(25, 55, 125, 0.3) !important;
}

.property-table :deep(.el-table__row:hover) {
  background: rgba(2, 7, 11, 0.1) !important;
  transform: scale(1.01) !important;
}

.property-table :deep(.el-table__body tr:hover>td.el-table__cell){
  background: rgba(0, 149, 255, 0.1) !important;
  transform: scale(1.01) !important;
}


.property-table :deep(.el-table__row td) {
  border-bottom: 1px solid rgba(0, 149, 255, 0.1) !important;
  border-right: 1px solid rgba(0, 149, 255, 0.1) !important;
  padding: 10px 8px !important;
}

.property-table :deep(.el-table__row td:last-child) {
  border-right: none !important;
}

.property-table :deep(.el-table__border-left-patch) {
  background: transparent !important;
}

.property-table :deep(.el-table__border-right-patch) {
  background: transparent !important;
}

.property-table :deep(.el-table__empty-text) {
  color: rgba(255, 255, 255, 0.5) !important;
}

.property-table :deep(.el-loading-mask) {
  background-color: rgba(20, 50, 120, 0.8) !important;
}

.property-table :deep(.el-loading-text) {
  color: #00feff !important;
}

.property-table :deep(.el-table--fit .el-table__inner-wrapper:before) {
  width: 0px !important;
}


/* 表格内容样式 */
.unit-number {
  color: #00feff !important;
  font-weight: 600 !important;
}

.building-number {
  color: #9c27b0 !important;
  font-weight: 600 !important;
}

.highlight {
  color: #00ff88 !important;
  font-weight: 600 !important;
}

/* 状态标签样式 */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid;
  display: inline-block;
  text-align: center;
  min-width: 60px;
}

.status-occupied {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border-color: rgba(76, 175, 80, 0.3);
}

.status-vacant {
  background: rgba(158, 158, 158, 0.2);
  color: #9e9e9e;
  border-color: rgba(158, 158, 158, 0.3);
}

.status-renovating {
  background: rgba(255, 167, 38, 0.2);
  color: #ffa726;
  border-color: rgba(255, 167, 38, 0.3);
}

.status-unknown {
  background: rgba(158, 158, 158, 0.2);
  color: #9e9e9e;
  border-color: rgba(158, 158, 158, 0.3);
}

/* 缴费状态标签样式 */
.payment-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid;
  display: inline-block;
  text-align: center;
  min-width: 60px;
}

.payment-paid {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border-color: rgba(76, 175, 80, 0.3);
}

.payment-overdue {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border-color: rgba(244, 67, 54, 0.3);
}

.payment-partial {
  background: rgba(255, 167, 38, 0.2);
  color: #ffa726;
  border-color: rgba(255, 167, 38, 0.3);
}

.payment-unknown {
  background: rgba(158, 158, 158, 0.2);
  color: #9e9e9e;
  border-color: rgba(158, 158, 158, 0.3);
}

/* Element Plus 分页样式覆盖 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.4) 0%,
    rgba(25, 55, 125, 0.6) 100%);
  border-radius: 6px;
  margin-top: 15px;
}

.property-pagination :deep(.el-pagination) {
  color: rgba(255, 255, 255, 0.8) !important;
}

.property-pagination :deep(.el-pagination__total) {
  color: rgba(255, 255, 255, 0.8) !important;
}

.property-pagination :deep(.el-pagination__sizes .el-select .el-input__inner) {
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.8) 0%,
    rgba(25, 55, 125, 0.9) 100%) !important;
  border: 1px solid rgba(0, 149, 255, 0.3) !important;
  color: #fff !important;
}

.property-pagination :deep(.el-pagination__sizes .el-select .el-input__inner:focus) {
  border-color: #00feff !important;
  box-shadow: 0 0 8px rgba(0, 254, 255, 0.3) !important;
}

.property-pagination :deep(.el-select-dropdown) {
  background: rgba(20, 50, 120, 0.95) !important;
  border: 1px solid rgba(0, 149, 255, 0.3) !important;
}

.property-pagination :deep(.el-select-dropdown__item) {
  color: #fff !important;
  background: transparent !important;
}

.property-pagination :deep(.el-select-dropdown__item:hover) {
  background: rgba(0, 149, 255, 0.2) !important;
}

.property-pagination :deep(.el-select-dropdown__item.selected) {
  background: rgba(0, 149, 255, 0.4) !important;
  color: #00feff !important;
}

.property-pagination :deep(.btn-prev),
.property-pagination :deep(.btn-next) {
  background: rgba(25, 55, 125, 0.6) !important;
  /* background: linear-gradient(135deg,
    rgba(0, 149, 255, 0.6) 0%,
    rgba(0, 149, 255, 0.8) 100%) !important; */
  border: 1px solid rgba(0, 149, 255, 0.3) !important;
  color: #fff !important;
}

.property-pagination :deep(.btn-prev:hover),
.property-pagination :deep(.btn-next:hover) {
  background: rgba(25, 55, 125, 0.6) !important;
  /* background: linear-gradient(135deg,
    rgba(0, 149, 255, 0.8) 0%,
    rgba(0, 149, 255, 1) 100%) !important; */
  box-shadow: 0 0 8px rgba(0, 149, 255, 0.4) !important;
}

.property-pagination :deep(.btn-prev:disabled),
.property-pagination :deep(.btn-next:disabled) {
  background: rgba(25, 55, 125, 0.4) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.3) !important;
}

.property-pagination :deep(.el-pager li) {
  background: rgba(25, 55, 125, 0.6) !important;
  border: 1px solid rgba(0, 149, 255, 0.2) !important;
  color: rgba(255, 255, 255, 0.8) !important;
  margin: 0 2px !important;
}

.property-pagination :deep(.el-pager li:hover) {
  background: rgba(0, 149, 255, 0.3) !important;
  border-color: rgba(0, 149, 255, 0.4) !important;
  color: white !important;
}

.property-pagination :deep(.el-pager li.is-active) {
  background: linear-gradient(135deg,
    rgba(0, 149, 255, 0.8) 0%,
    rgba(0, 254, 255, 0.6) 100%) !important;
  border-color: rgba(0, 149, 255, 0.6) !important;
  color: white !important;
  font-weight: 600 !important;
  box-shadow: 0 0 8px rgba(0, 149, 255, 0.4) !important;
}

.property-pagination :deep(.el-pagination__jump) {
  color: rgba(255, 255, 255, 0.8) !important;
}

.property-pagination :deep(.el-pagination__editor .el-input__inner) {
  background: linear-gradient(135deg,
    rgba(20, 50, 120, 0.8) 0%,
    rgba(25, 55, 125, 0.9) 100%) !important;
  border: 1px solid rgba(0, 149, 255, 0.3) !important;
  color: #fff !important;
}

.property-pagination :deep(.el-pagination__editor .el-input__inner:focus) {
  border-color: #00feff !important;
  box-shadow: 0 0 8px rgba(0, 254, 255, 0.3) !important;
}

/* 滚动条样式 */
.property-table :deep(.el-table__body-wrapper)::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.property-table :deep(.el-table__body-wrapper)::-webkit-scrollbar-track {
  background: rgba(20, 50, 120, 0.3);
  border-radius: 4px;
}

.property-table :deep(.el-table__body-wrapper)::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #00feff, rgba(0, 149, 255, 0.8));
  border-radius: 4px;
  box-shadow: inset 0 0 3px rgba(0, 254, 255, 0.3);
}

.property-table :deep(.el-table__body-wrapper)::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #00feff, #0095ff);
}
</style>
