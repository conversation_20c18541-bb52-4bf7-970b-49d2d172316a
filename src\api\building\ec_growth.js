import request from '@/utils/request'
// 查询每年企业/住宅/公寓数量列表
export function listEcGrowth(query) {
  return request({
    url: '/building/ec_growth/list',
    method: 'get',
    params: query
  })
}

// 查询每年企业/住宅/公寓数量详细
export function getEcGrowth(id) {
  return request({
    url: '/building/ec_growth/' + id,
    method: 'get'
  })
}

// 新增每年企业/住宅/公寓数量
export function addEcGrowth(data) {
  return request({
    url: '/building/ec_growth/add',
    method: 'post',
    data: data
  })
}

// 修改每年企业/住宅/公寓数量
export function updateEcGrowth(data) {
  return request({
    url: '/building/ec_growth/edit',
    method: 'post',
    data: data
  })
}

// 删除每年企业/住宅/公寓数量
export function delEcGrowth(id) {
  return request({
    url: '/building/ec_growth/' + id,
    method: 'delete'
  })
}

