<template>
  <el-form ref="genInfoForm" :model="info" :rules="rules" label-width="150px">
    <el-row>
      <el-col :span="12">
        <el-form-item prop="tplCategory" label-width="400">
          <template #label>生成模板</template>
          <el-select v-model="info.tplCategory">
            <el-option label="单表（增删改查）" value="crud" />
            <el-option label="树表（增删改查）" value="tree" />
            <el-option label="主子表（增删改查）" value="sub" />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item prop="tplWebType">
          <template #label>前端类型</template>
          <el-select v-model="info.tplWebType">
            <el-option label="Vue2 Element UI 模版" value="element-ui" />
            <el-option label="Vue3 Element Plus 模版" value="element-plus" />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item prop="functionName" label-width="400">
          <template #label>
            生成功能名
            <el-tooltip content="用作类描述，例如 用户" placement="top">
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
          </template>
          <el-input v-model="info.functionName" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item >
          <template #label>
            上级菜单
            <el-tooltip content="分配到指定菜单下，例如 系统管理" placement="top">
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
          </template>
          <el-tree-select
            v-model="info.parentMenuId"
            :data="menuOptions"
            :props="{ value: 'menuId', label: 'menuName', children: 'children' }"
            value-key="menuId"
            placeholder="请选择系统菜单"
            check-strictly
          />
        </el-form-item>
      </el-col>
      

      <el-col :span="24">
        <el-form-item prop="packageName" label-width="400">
          <template #label>
            生成包路径（com.business.xxxx）
            <el-tooltip content="生成在哪个java包下，例如 com.business.xxxx" placement="top">
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
          </template>
          <el-input v-model="info.packageName" />
        </el-form-item>
      </el-col>

      <el-col :span="24">
        <el-form-item prop="moduleName"  label-width="400">
          <template #label>
            生成模块名（com.business.xxxx，xxxx代表模块名称"）
            <el-tooltip content="可理解为子系统名，功能模块名称，com.business.xxxx，xxxx代表模块名称" placement="top">
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
          </template>
          <el-input v-model="info.moduleName" />
        </el-form-item>
      </el-col>



      
      <el-col :span="12">
        <el-form-item prop="businessName" label-width="400">
          <template #label>
            生成业务名（表名）
            <el-tooltip content="表名： user" placement="top">
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
          </template>
          <el-input v-model="info.businessName" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item prop="genType">
          <template #label>
            生成代码方式
            <el-tooltip content="默认为zip压缩包下载，也可以自定义生成路径" placement="top">
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
          </template>
          <el-radio v-model="info.genType" value="0">zip压缩包</el-radio>
          <el-radio v-model="info.genType" value="1">自定义路径</el-radio>
        </el-form-item>
      </el-col>


      <el-col :span="24" v-if="info.genType == '1'">
        <el-form-item prop="genPath"  label-width="400">
          <template #label>
            业务逻辑生成路径
            <el-tooltip content="填写磁盘绝对路径，若不填写，则生成到当前Web项目下" placement="top">
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
          </template>
          <el-input v-model="info.genPath">
            <template #append>
              <el-dropdown>
                <el-button type="primary">
                  最近路径快速选择
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="getrootPath(1)">恢复默认的生成基础路径</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-input>
        </el-form-item>
      </el-col>

      <el-col :span="24" v-if="info.genType == '1'">
        <el-form-item prop="genPath"  label-width="400">
          <template #label>
            Vue页面路径
            <el-tooltip content="填写磁盘绝对路径，若不填写，则生成到当前Web项目下" placement="top">
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
          </template>
          <el-input v-model="info.genVuePath">
            <template #append>
              <el-dropdown>
                <el-button type="primary">
                  最近路径快速选择
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="getrootPath(2)">恢复默认的生成基础路径</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
    
    <template v-if="info.tplCategory == 'tree'">
      <h4 class="form-header">其他信息</h4>
      <el-row v-show="info.tplCategory == 'tree'">
        <el-col :span="12">
          <el-form-item>
            <template #label>
              树编码字段
              <el-tooltip content="树显示的编码字段名， 如：dept_id" placement="top">
                <el-icon><question-filled /></el-icon>
              </el-tooltip>
            </template>
            <el-select v-model="info.treeCode" placeholder="请选择">
              <el-option
                v-for="(column, index) in info.columns"
                :key="index"
                :label="column.columnName + '：' + column.columnComment"
                :value="column.columnName"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item>
            <template #label>
              树父编码字段
              <el-tooltip content="树显示的父编码字段名， 如：parent_Id" placement="top">
                <el-icon><question-filled /></el-icon>
              </el-tooltip>
            </template>
            <el-select v-model="info.treeParentCode" placeholder="请选择">
              <el-option
                v-for="(column, index) in info.columns"
                :key="index"
                :label="column.columnName + '：' + column.columnComment"
                :value="column.columnName"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item>
            <template #label>
              树名称字段
              <el-tooltip content="树节点的显示名称字段名， 如：dept_name" placement="top">
                <el-icon><question-filled /></el-icon>
              </el-tooltip>
            </template>
            <el-select v-model="info.treeName" placeholder="请选择">
              <el-option
                v-for="(column, index) in info.columns"
                :key="index"
                :label="column.columnName + '：' + column.columnComment"
                :value="column.columnName"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </template>

    <template v-if="info.tplCategory == 'sub'">
      <h4 class="form-header">关联信息</h4>
      <div v-for="(item, index) in subTable" :key="index">
        <el-row>
          <el-col :span="12">
            <el-form-item>
              <template #label>
                关联子表的表名
                <el-tooltip content="关联子表的表名， 如：sys_user" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
              </template>
            <el-select v-model="item.subTableName" placeholder="请选择" @change="(value) => subSelectChange(value, index)">
                <el-option
                  v-for="(table, index) in tables"
                  :key="index"
                  :label="table.tableName + '：' + table.tableComment"
                  :value="table.tableName"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <template #label>
                子表关联的外键名
                <el-tooltip content="子表关联的外键名， 如：user_id" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
              </template>
              <el-select v-model="item.subTableFkName" placeholder="请选择" @change="(value) => subColumSelectChange(value, index)">
                <el-option
                v-for="(column, index) in item.subColumns"
                  :key="index"
                  :label="column.columnName + '：' + column.columnComment"
                  :value="column.columnName"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <el-row>
        <el-col :span="24">
          <el-form-item>
            <el-button type="primary" @click="handleBatchAdd">新增子表</el-button>
            <el-button type="danger" @click="handleBatchDel" style="width:90px" >删除</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </template>

  </el-form>
</template>

<script setup>
import { listMenu,getConfig} from "@/api/system/menu";

const subTable = ref([]);
const subColumns = ref([]);
const menuOptions = ref([]);
const { proxy } = getCurrentInstance();

const props = defineProps({
  info: {
    type: Object,
    default: null
  },
  tables: {
    type: Array,
    default: null
  },
  subTables: {
    type: Array,
    default: null
  }
});

// 表单校验
const rules = ref({
  tplCategory: [{ required: true, message: "请选择生成模板", trigger: "blur" }],
  packageName: [{ required: true, message: "请输入生成包路径", trigger: "blur" }],
  moduleName: [{ required: true, message: "请输入生成模块名", trigger: "blur" }],
  businessName: [{ required: true, message: "请输入生成业务名", trigger: "blur" }],
  functionName: [{ required: true, message: "请输入生成功能名", trigger: "blur" }]
});

//关联子表下拉
function subSelectChange(value,index) {  
  setSubTableColumns(value,index);
}
//关联子表字段下拉
function subColumSelectChange(value,index) {  
    subTable.value[index].subTableFkName = value;
    emit("updateSubTables", subTable.value); //调研父窗体绑定的监听事件
}

//定义父窗体变量修改事件
const emit = defineEmits(["updateSubTables"]) //子窗体实例化时绑定的函数


//获取子表下拉后关联子表字段
function setSubTableColumns(value,index) {
  for (var item in props.tables) {
    const name = props.tables[item].tableName;
    if (value === name) {
      subColumns.value = props.tables[item].columns;
      subTable.value[index].tableId = props.tables[item].tableId;
      subTable.value[index].subTableName = name;
      subTable.value[index].subColumns =  props.tables[item].columns;
      break;
    }
  }
}

/** 获取vue和java保存地址 */
function getrootPath(type) {
    getConfig().then(response=>{
        if(type==1)
        {
            props.info.genPath =  response.data.javaPath;
        }
        else if(type==2)
        {
            props.info.genVuePath =  response.data.vuePath;
        }
    });
}

/** 查询菜单下拉树结构 */
function getMenuTreeselect() {
  listMenu().then(response => {
    menuOptions.value = proxy.handleTree(response.data, "menuId");
  });
}

onMounted(() => {
  getMenuTreeselect();// 添加新的子表行
})

watch(() => props.info.tplWebType, val => {
  if (val === '') {
    props.info.tplWebType = "element-plus";
  }
});

//监听主表对象变化，用于初始化子窗体对象
watch(() => props.subTables, val => {
   subTable.value = val;
});

// 批量新增行事件
function handleBatchAdd() {
  if (props.info.tplCategory !== 'sub') {
    proxy.$modal.msgError("只有主子表模式下才能批量新增行");
    return;
  }
   
  //添加新的子表行，通过watch监听更新父类对象
  subTable.value.push({
      tableId: 0,
      subTableName: "",
      subTableFkName: "",
      subColumns:[]
  });  
}

//删除行事件
function handleBatchDel(){
    subTable.value.pop();  
    emit("updateSubTables", subTable.value); //同步给父窗体
}

</script>
